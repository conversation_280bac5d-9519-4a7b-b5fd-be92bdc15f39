import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as sqs from 'aws-cdk-lib/aws-sqs';
import { ServiceConfig } from '../shared/service-name-util';

export class QueueStack extends cdk.Stack {
  public readonly recordFifoScheduleQueue: sqs.Queue;

  public readonly singleRecordRequestQueue: sqs.Queue;

  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);
    const queueName = new ServiceConfig().NameGenerator(
      'record-fifo-schedule-queue.fifo'
    );
    this.recordFifoScheduleQueue = new sqs.Queue(
      this,
      new ServiceConfig().NameGenerator('record-fifo-schedule-queue'),
      {
        queueName: queueName,
        fifo: true,
        contentBasedDeduplication: true,
        visibilityTimeout: cdk.Duration.seconds(900),
      }
    );
    const singleQueueName = new ServiceConfig().NameGenerator(
      'single-record-request-queue.fifo'
    );
    this.singleRecordRequestQueue = new sqs.Queue(
      this,
      new ServiceConfig().NameGenerator('single-record-request-queue'),
      {
        queueName: singleQueueName,
        fifo: true,
        contentBasedDeduplication: true,
        visibilityTimeout: cdk.Duration.minutes(20),
      }
    );
  }
}
