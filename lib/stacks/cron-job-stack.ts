import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as events from 'aws-cdk-lib/aws-events';
import * as targets from 'aws-cdk-lib/aws-events-targets';
import { BasicLambdaFunction } from '../shared/patterns/basic-lambda-function';
import { ServiceConfig } from '../shared/service-name-util';
import { QueueStack } from './sqs-queue-stack';
import { SecretManagerStack } from './secret-manager-stack';

interface IRequestRecordDataFlowStackProps extends StackProps {
  queueStack: QueueStack;
  secretManagerStack: SecretManagerStack;
}
export class CronJobStack extends Stack {
  private readonly queueStack: QueueStack;

  private readonly secretManagerStack: SecretManagerStack;

  constructor(
    scope: Construct,
    id: string,
    props: IRequestRecordDataFlowStackProps
  ) {
    super(scope, id, props);
    this.queueStack = props.queueStack;
    this.secretManagerStack = props.secretManagerStack;
    this.scheduleRequestCronJobUpsertRecordsQueue();
  }

  private scheduleRequestCronJobUpsertRecordsQueue() {
    const functionName = new ServiceConfig().NameGenerator(
      'schedule-upsert-records-queue'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/record/use-case/schedule-requests/schedule-request-cron-job-upsert-records-queue/handler.ts',
      functionProps: {
        environment: {
          SQS_QUEUE_URL: this.queueStack.recordFifoScheduleQueue.queueUrl,
          ZOHO_SECRET_ID: this.secretManagerStack.zohoClientAPIData.secretArn,
        },
      },
    });
    this.secretManagerStack.zohoClientAPIData.grantRead(resolver);
    this.secretManagerStack.zohoClientAPIData.grantWrite(resolver);
    this.queueStack.recordFifoScheduleQueue.grantSendMessages(resolver);
    new events.Rule(this, 'schedule-request-7PM', {
      schedule: events.Schedule.cron({ minute: '0', hour: '1' }), // 1:00 AM UTC = 7:00 PM CST
      targets: [new targets.LambdaFunction(resolver)],
    });
  }
}
