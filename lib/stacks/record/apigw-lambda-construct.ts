/* eslint-disable @typescript-eslint/naming-convention */
import { Resource, LambdaIntegration } from 'aws-cdk-lib/aws-apigateway';
import { Construct } from 'constructs';
import { ServiceConfig } from '../../shared/service-name-util';
import { DynamoDBStack } from '../dynamodb-stack';
import { ApiKeyAPIGWEndpoint } from '../../shared/patterns/apikey-apigw-endpoint-construct';
import { BasicLambdaFunction } from '../../shared/patterns/basic-lambda-function';
import { QueueStack } from '../sqs-queue-stack';

interface IApiGWLambdaStackProps {
  dynamodbStack: DynamoDBStack;
  queueStack: QueueStack;
}
export class ApiGWLambdaConstruct extends Construct {
  private readonly dynamodbStack;

  private readonly apigw;

  private recordResource: Resource;

  private readonly queueStack: QueueStack;

  constructor(scope: Construct, id: string, props: IApiGWLambdaStackProps) {
    super(scope, id);
    this.dynamodbStack = props.dynamodbStack;
    this.queueStack = props.queueStack;
    const apiId = new ServiceConfig().NameGenerator('record-api');
    this.apigw = new ApiKeyAPIGWEndpoint(this, apiId, {
      id: apiId,
      name: apiId,
      apiKeyName: apiId,
    });
    this.initRecordResource();
  }

  private initRecordResource() {
    this.recordResource = this.addResource('record')
      .addResource('request')
      .addResource('{recordId}');
    this.addGetRecord();
  }

  private addGetRecord() {
    const functionName = new ServiceConfig().NameGenerator(
      'request-record-data-get'
    );
    const resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/record/use-case/request-record-data/handler.ts',
      functionProps: {
        environment: {
          RECORD_TABLE: this.dynamodbStack.recordsTable.tableName,
          SQS_QUEUE_URL: this.queueStack.singleRecordRequestQueue.queueUrl,
        },
      },
    });
    this.dynamodbStack.recordsTable.grantReadData(resolver);
    this.queueStack.singleRecordRequestQueue.grantSendMessages(resolver);
    const requestValidator = this.apigw.api.addRequestValidator(
      'RequestValidator',
      {
        validateRequestParameters: true,
        requestValidatorName: 'record-request-validator',
      }
    );
    this.recordResource.addMethod('GET', new LambdaIntegration(resolver), {
      apiKeyRequired: true,
      requestValidator: requestValidator,
      requestParameters: {
        'method.request.path.recordId': true,
        //TODO: Uncomment when query string is needed
        // 'method.request.querystring.userEmail': true,
      },
    });
  }

  private addResource(resourceName: string) {
    return this.apigw.api.root.addResource(resourceName);
  }
}
