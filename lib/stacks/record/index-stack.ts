import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { ServiceConfig } from '../../shared/service-name-util';
import { ApiGWLambdaConstruct } from './apigw-lambda-construct';
import { DynamoDBStack } from '../dynamodb-stack';
import { RequestRecordDataFlowStack } from './update-record-flow-stack';
import { SecretManagerStack } from '../secret-manager-stack';
import { RecordProcessorStack } from './record-processor-stack';
import { QueueStack } from '../sqs-queue-stack';

interface IRecordStackProps extends StackProps {
  dynamodbStack: DynamoDBStack;
  secretManagerStack: SecretManagerStack;
  queueStack: QueueStack;
}
export class RecordStack extends Stack {
  public readonly recordLambda: ApiGWLambdaConstruct;

  constructor(scope: Construct, id: string, props: IRecordStackProps) {
    super(scope, id, props);
    const service = new ServiceConfig();
    const { dynamodbStack, queueStack, secretManagerStack } = props;
    const requestRecordDataFlow = new RequestRecordDataFlowStack(
      this,
      service.StackNameGenerator('request-record-data-flow'),
      { dynamodbStack, queueStack, secretManagerStack }
    );
    this.recordLambda = new ApiGWLambdaConstruct(
      this,
      service.StackNameGenerator('record-apigw-lambda'),
      {
        dynamodbStack,
        queueStack,
      }
    );
    new RecordProcessorStack(
      this,
      service.StackNameGenerator('request-record-processor'),
      {
        queueStack,
        ExternalDataRequestResolver: requestRecordDataFlow.resolver,
      }
    );
  }
}
