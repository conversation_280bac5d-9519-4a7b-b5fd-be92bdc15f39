import { Duration, Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { SqsEventSource } from 'aws-cdk-lib/aws-lambda-event-sources';
import { ServiceConfig } from '../../shared/service-name-util';
import { BasicLambdaFunction } from '../../shared/patterns/basic-lambda-function';
import { QueueStack } from '../sqs-queue-stack';
import { NodejsFunction } from 'aws-cdk-lib/aws-lambda-nodejs';

interface IProcessorStackProps extends StackProps {
  queueStack: QueueStack;
  ExternalDataRequestResolver: NodejsFunction;
}
export class RecordProcessorStack extends Stack {
  private readonly queueStack: QueueStack;

  private readonly externalDataRequestResolver: NodejsFunction;

  constructor(scope: Construct, id: string, props: IProcessorStackProps) {
    super(scope, id, props);
    this.queueStack = props.queueStack;
    this.externalDataRequestResolver = props.ExternalDataRequestResolver;
    this.addProcessorFunction();
  }

  private addProcessorFunction() {
    const functionName = new ServiceConfig().NameGenerator('record-processor');
    const processorFunction = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/record/use-case/schedule-requests/record-processor/handler.ts',
      functionProps: {
        environment: {
          PROCESSOR_FUNCTION_NAME:
            this.externalDataRequestResolver.functionName,
        },
        timeout: Duration.minutes(15),
      },
    });
    this.externalDataRequestResolver.grantInvoke(processorFunction);
    this.queueStack.recordFifoScheduleQueue.grantConsumeMessages(
      processorFunction
    );
    processorFunction.addEventSource(
      new SqsEventSource(this.queueStack.recordFifoScheduleQueue, {
        batchSize: 1,
      })
    );
  }
}
