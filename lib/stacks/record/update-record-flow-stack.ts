import { Construct } from 'constructs';
import { ServiceConfig } from '../../shared/service-name-util';
import { DynamoDBStack } from '../dynamodb-stack';
import { Duration, Stack, StackProps } from 'aws-cdk-lib';
import { BasicLambdaFunction } from '../../shared/patterns/basic-lambda-function';
import { SecretManagerStack } from '../secret-manager-stack';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import { NodejsFunction } from 'aws-cdk-lib/aws-lambda-nodejs';
import { PolicyStatement } from 'aws-cdk-lib/aws-iam';
import { SendEmailPolicyStatement } from '../../shared/patterns/send-email-policy-statement';
import { QueueStack } from '../sqs-queue-stack';
import { SqsEventSource } from 'aws-cdk-lib/aws-lambda-event-sources';

interface IRequestRecordDataFlowStackProps extends StackProps {
  dynamodbStack: DynamoDBStack;
  queueStack: QueueStack;
  secretManagerStack: SecretManagerStack;
}
export class RequestRecordDataFlowStack extends Stack {
  private readonly dynamodbStack: DynamoDBStack;

  private readonly secretManagerStack: SecretManagerStack;

  public resolver: NodejsFunction;

  private readonly queueStack: QueueStack;

  constructor(
    scope: Construct,
    id: string,
    props: IRequestRecordDataFlowStackProps
  ) {
    super(scope, id, props);
    this.dynamodbStack = props.dynamodbStack;
    this.secretManagerStack = props.secretManagerStack;
    this.queueStack = props.queueStack;
    this.initFlow();
  }

  private initFlow() {
    const functionName = new ServiceConfig().NameGenerator(
      'get-external-record-data-flow-post'
    );
    this.resolver = BasicLambdaFunction(this, {
      id: functionName,
      name: functionName,
      handler: 'handler',
      functionPath:
        '../../../app/modules/record/use-case/get-external-record-data/handler.ts',
      functionProps: {
        bundling: {
          externalModules: ['@sparticuz/chromium'],
        },
        layers: [
          lambda.LayerVersion.fromLayerVersionArn(
            this,
            'chromium-lambda-layer',
            'arn:aws:lambda:us-east-1:764866452798:layer:chrome-aws-lambda:50'
          ),
        ],
        memorySize: 1600,
        timeout: Duration.minutes(15),
        environment: {
          RECORD_TABLE: this.dynamodbStack.recordsTable.tableName,
          JUDICIARY_AUTH_DATA_TABLE:
            this.dynamodbStack.judiciaryAuthDataTable.tableName,
          ZOHO_SECRET_ID: this.secretManagerStack.zohoClientAPIData.secretArn,
          ZOHO_WORK_DRIVE_FOLDER_PARENT_ID:
            process.env.ZOHO_WORK_DRIVE_FOLDER_PARENT_ID || '',
        },
      },
    });
    this.resolver.addToRolePolicy(
      new PolicyStatement(SendEmailPolicyStatement())
    );
    this.secretManagerStack.zohoClientAPIData.grantRead(this.resolver);
    this.secretManagerStack.zohoClientAPIData.grantWrite(this.resolver);
    this.dynamodbStack.recordsTable.grantReadWriteData(this.resolver);
    this.dynamodbStack.judiciaryAuthDataTable.grantReadWriteData(this.resolver);
    this.queueStack.singleRecordRequestQueue.grantConsumeMessages(
      this.resolver
    );
    this.resolver.addEventSource(
      new SqsEventSource(this.queueStack.singleRecordRequestQueue, {
        batchSize: 1,
      })
    );
  }
}
