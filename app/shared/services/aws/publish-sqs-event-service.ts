/* eslint-disable no-console */
import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';
import { Result } from '../../core/Result';

interface IEventServiceProps {
  body: string;
  author?: string;
  url?: string;
  delay?: number;
  messageGroupId?: string;
}
export class EventService {
  private url: string;

  private author: string;

  private sqsClient: SQSClient;

  constructor(url: string, author: string, region = 'us-east-1') {
    this.url = url;
    this.author = author;
    this.sqsClient = new SQSClient({ region });
  }

  public async publishEvent({
    body,
    author,
    url,
    delay,
    messageGroupId,
  }: IEventServiceProps): Promise<Result<string>> {
    const params = {
      DelaySeconds: delay || 0,
      MessageAttributes: {
        Author: {
          DataType: 'String',
          StringValue: author || this.author,
        },
      },
      MessageBody: body,
      QueueUrl: url || this.url,
      MessageGroupId: messageGroupId,
    };
    try {
      const result = await this.sqsClient.send(new SendMessageCommand(params));
      console.log(`Message id: ${result.MessageId}`);
      console.log(
        `Message published to SQS queue ${params.QueueUrl}: ${result.MessageId}`
      );

      return Result.ok<string>(result.MessageId);
    } catch (error) {
      console.log('Error publishing message', error);

      return Result.fail<string>(`Error publishing message: ${error}`);
    }
  }
}
