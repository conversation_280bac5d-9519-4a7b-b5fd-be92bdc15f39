import { DomainError } from '../domain/errors/domain/domain-error';
import { ErrorFactory } from '../domain/errors/domain/error-factory';
import { ErrorNotificationUseCase } from '../../modules/record/use-case/error-notification/use-case';

// eslint-disable-next-line @typescript-eslint/naming-convention
export interface ErrorHandlerOptions {
  shouldRethrow?: boolean;
  shouldLog?: boolean;
  context?: Record<string, unknown>;
}
export class ErrorHandler {
  constructor(
    private readonly errorNotificationUseCase: ErrorNotificationUseCase
  ) {}

  public async handleError(
    error: unknown,
    recordId: string,
    options: ErrorHandlerOptions = {}
  ): Promise<void> {
    const { shouldRethrow = false, shouldLog = true, context = {} } = options;
    const domainError = ErrorFactory.createFromError(error, recordId, context);
    if (shouldLog) {
      this.logError(domainError);
    }
    await this.notifyError(
      domainError,
      options.context?.userEmail as string | undefined
    );
    if (shouldRethrow) {
      throw domainError;
    }
  }

  private logError(error: DomainError): void {
    const serialized = error.serialize();
    // eslint-disable-next-line no-console
    console.error(`[ERROR][${serialized.category}] ${serialized.message}`, {
      errorCode: serialized.code,
      errorName: serialized.name,
      details: serialized.details,
    });
  }

  private async notifyError(
    error: DomainError,
    userEmail?: string
  ): Promise<void> {
    try {
      await this.errorNotificationUseCase.execute({
        error: error.serialize(),
        userEmail,
      });
    } catch (notificationError) {
      // eslint-disable-next-line no-console
      console.error('Failed to notify error', {
        originalError: error.message,
        notificationError:
          notificationError instanceof Error
            ? notificationError.message
            : String(notificationError),
      });
    }
  }
}
