/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/naming-convention */
import { Result } from '../../../../core/Result';

export interface ExternalCredentialStorageService {
  searchAll: () => Promise<Result<SearchAllResponseDTO>>;
  searchCredentialsByInternalRecordId: (
    id: string
  ) => Promise<Result<SearchCredentialsByRecordIdResponseDTO>>;

  searchByZohoId: (id: string) => Promise<Result<SearchByZohoIdResponseDTO>>;
}
export type SearchAllRequestResponseDTO = {
  data: {
    id: string;
    Usuario: string;
    Password: string;
  }[];
  info: {
    per_page: number;
    next_page_token: string | null;
    count: number;
    sort_by: string;
    page: number;
    previous_page_token: string | null;
    page_token_expiry: string | null;
    sort_order: 'asc' | 'desc';
    more_records: boolean;
  };
};
export type SearchAllResponseDTO = {
  id: string;
  Usuario: string;
  Password: string;
  Expedientes_a_los_que_tiene_acceso: ExpedienteAcceso[];
}[];
export type SearchByZohoIdResponseDTO = {
  data: RecordData[];
};
export type SearchCredentialsByRecordIdResponse = {
  data: {
    id: string;
    Usuario_del_PJ: {
      id: string;
    };
  }[];
};
export type SearchCredentialsByRecordIdResponseDTO = {
  id: string;
  Usuario_del_PJ: {
    id: string;
  };
}[];
interface RecordData {
  Owner: UserInfo;
  Email: string | null;
  Usuario: string;
  $currency_symbol: string;
  $field_states: any; // Puedes especificar el tipo exacto si se conoce
  $review_process: ReviewProcess;
  $layout_id: Layout;
  $sharing_permission: string;
  Name: string;
  Last_Activity_Time: string;
  Modified_By: UserInfo;
  $review: any; // Puedes especificar el tipo exacto si se conoce
  Unsubscribed_Mode: any; // Puedes especificar el tipo exacto si se conoce
  $process_flow: boolean;
  $locked_for_me: boolean;
  id: string;
  $zia_visions: any; // Puedes especificar el tipo exacto si se conoce
  Password: string;
  $approval: Approval;
  Modified_Time: string;
  Nombre_del_Usuario: string;
  Created_Time: string;
  Unsubscribed_Time: any; // Puedes especificar el tipo exacto si se conoce
  $wizard_connection_path: any; // Puedes especificar el tipo exacto si se conoce
  $editable: boolean;
  Record_Status__s: string;
  Expedientes_a_los_que_tiene_acceso: ExpedienteAcceso[];
  $orchestration: boolean;
  $in_merge: boolean;
  Locked__s: boolean;
  Created_By: UserInfo;
  Tag: any[]; // Ajusta el tipo según la estructura real de los tags
  $zia_owner_assignment: string;
  $approval_state: string;
  $pathfinder: boolean;
  $has_more: HasMore;
}
interface UserInfo {
  name: string;
  id: string;
  email: string;
}
interface ReviewProcess {
  approve: boolean;
  reject: boolean;
  resubmit: boolean;
}
interface Layout {
  name: string;
  id: string;
}
interface Approval {
  delegate: boolean;
  takeover: boolean;
  approve: boolean;
  reject: boolean;
  resubmit: boolean;
}
interface ExpedienteAcceso {
  id: string;
  Expedientes_a_los_que_tiene_acceso: {
    name: string;
    id: string;
  };
}
interface HasMore {
  Expedientes_a_los_que_tiene_acceso: boolean;
}
