import { Result } from '../../../../core/Result';
import { ZohoAPIService } from '../zoho-api-services';
import {
  SearchAllResponseDTO,
  ExternalCredentialStorageService,
  SearchByZohoIdResponseDTO,
  SearchAllRequestResponseDTO,
  SearchCredentialsByRecordIdResponseDTO,
  SearchCredentialsByRecordIdResponse,
} from './type';

export class ZohoCredentialService
  extends ZohoAPIService
  implements ExternalCredentialStorageService
{
  public async searchAll(): Promise<Result<SearchAllResponseDTO>> {
    try {
      const module = 'Usuarios_PJ';
      const params = `fields=Id,Usuario,Password&per_page=200&page=1`;
      const response = await this.executeRequest<SearchAllRequestResponseDTO>({
        method: 'get',
        path: `${module}?${params}`,
        isCacheEnabled: true,
      });
      // eslint-disable-next-line no-console
      console.log(
        '[ZohoCredentialService] ZohoCredentialService searchAll response',
        response
      );
      const aggregatedResponse: SearchAllResponseDTO = await Promise.all(
        response.data.map(async (item) => {
          const credentialDetailOrError = await this.searchByZohoId(item.id);
          if (credentialDetailOrError.isFailure) {
            return {
              ...item,
              // eslint-disable-next-line @typescript-eslint/naming-convention
              Expedientes_a_los_que_tiene_acceso: [],
            };
          }
          const credentialDetail = credentialDetailOrError.getValue().data[0];

          return {
            ...item,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            Expedientes_a_los_que_tiene_acceso:
              credentialDetail.Expedientes_a_los_que_tiene_acceso,
          };
        })
      );

      return Result.ok(aggregatedResponse);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(
        '[ZohoCredentialService] Error searching credentials in Zoho:',
        JSON.stringify(error)
      );

      return Result.fail<SearchAllResponseDTO>(
        `Error searching credentials in Zoho:${JSON.stringify(error)}`
      );
    }
  }

  public async searchCredentialsByInternalRecordId(
    internalRecordId: string
  ): Promise<Result<SearchCredentialsByRecordIdResponseDTO>> {
    try {
      // eslint-disable-next-line no-console
      console.log(
        `[ZohoRecordService] searchCredentialsByRecordId in Zoho for recordId ${internalRecordId}`
      );
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const select_query = `SELECT Usuario_del_PJ FROM Expedientes_X_Usuarios_PJ WHERE Expedientes_a_los_que_tiene_acceso = '${internalRecordId}'`;
      const response =
        await this.executeRequest<SearchCredentialsByRecordIdResponse>({
          method: 'post',
          path: `coql`,
          data: {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            select_query: select_query,
          },
          isCacheEnabled: true,
        });
      // eslint-disable-next-line no-console
      console.log(
        '[ZohoRecordService] searchCredentialsByRecordId response',
        response
      );

      return Result.ok(response.data);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(
        `[ZohoRecordService] Error searching for credentials for record ${internalRecordId} from Zoho:`,
        JSON.stringify(error)
      );

      return Result.fail<SearchCredentialsByRecordIdResponseDTO>(
        `Error searching for credentials for record ${internalRecordId} from Zoho :${JSON.stringify(
          error
        )}`
      );
    }
  }

  public async searchByZohoId(
    id: string
  ): Promise<Result<SearchByZohoIdResponseDTO>> {
    try {
      const module = 'Usuarios_PJ';
      const response = await this.executeRequest<SearchByZohoIdResponseDTO>({
        method: 'get',
        path: `${module}/${id}`,
        isCacheEnabled: true,
      });
      // eslint-disable-next-line no-console
      console.log(
        '[ZohoCredentialService] ZohoCredentialService searchByZohoId response',
        response
      );

      return Result.ok(response);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(
        '[ZohoCredentialService] Error searching credentials in Zoho:',
        JSON.stringify(error)
      );

      return Result.fail<SearchByZohoIdResponseDTO>(
        `Error searching credentials in Zoho:${JSON.stringify(error)}`
      );
    }
  }
}
