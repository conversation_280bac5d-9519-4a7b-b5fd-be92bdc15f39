/* eslint-disable no-constant-condition */
/* eslint-disable @typescript-eslint/naming-convention */
import { Result } from '../../../../core/Result';
import { ZohoAPIService } from '../zoho-api-services';
import { RecordExternalStorageService } from './record-external-storage-service';
import {
  CreateRecordDTO,
  CreateRecordResponseDTO,
  GetRelatedListDTO,
  SearchByIdDTO,
  SearchByIdResponseDTO,
  UpdateRecordDTO,
  UpdateRecordResponseDTO,
} from './type';

export class ZohoRecordService
  extends ZohoAPIService
  implements RecordExternalStorageService
{
  public async searchForExternalId(
    dto: SearchByIdDTO
  ): Promise<Result<SearchByIdResponseDTO>> {
    try {
      // eslint-disable-next-line no-console
      console.log('[ZohoRecordService] searchForExternalId in Zoho:', dto);
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const select_query = `SELECT id FROM ${dto.module} WHERE ${dto.queryParam} = '${dto.queryValue}'`;
      const response = await this.executeRequest<SearchByIdResponseDTO>({
        method: 'post',
        path: `coql`,
        data: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          select_query: select_query,
        },
        isCacheEnabled: true,
      });
      // eslint-disable-next-line no-console
      console.log('[ZohoRecordService] searchForExternalId response', response);

      return Result.ok(response);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(
        `[ZohoRecordService] Error searching for external id in module ${dto.module} from Zoho:`,
        JSON.stringify(error)
      );

      return Result.fail<SearchByIdResponseDTO>(
        `Error searching for external id in module ${
          dto.module
        } from Zoho :${JSON.stringify(error)}`
      );
    }
  }

  public async create(
    dto: CreateRecordDTO
  ): Promise<Result<CreateRecordResponseDTO>> {
    try {
      // eslint-disable-next-line no-console
      console.log(`[ZohoRecordService] create ${dto.module} in Zoho:`, dto);
      const response = await this.executeRequest<CreateRecordResponseDTO>({
        method: 'post',
        path: dto.module,
        data: { data: [{ ...dto.data }] },
      });

      return Result.ok(response);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(
        `[ZohoRecordService] Error creating ${dto.module} in Zoho:`,
        JSON.stringify(error)
      );

      return Result.fail<CreateRecordResponseDTO>(
        `Error creating ${dto.module} in Zoho:${JSON.stringify(error)}`
      );
    }
  }

  public async update(
    dto: UpdateRecordDTO
  ): Promise<Result<UpdateRecordResponseDTO>> {
    try {
      const response = await this.executeRequest<UpdateRecordResponseDTO>({
        method: 'put',
        path: `${dto.module}/${dto.externalId}`,
        data: { data: [{ ...dto.data }] },
      });

      return Result.ok(response);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(
        `[ZohoRecordService] Error updating ${dto.module} in Zoho:`,
        JSON.stringify(error)
      );

      return Result.fail<UpdateRecordResponseDTO>(
        `Error updating ${dto.module} in Zoho:${JSON.stringify(error)}`
      );
    }
  }

  public async getRelatedList<T>(dto: GetRelatedListDTO): Promise<Result<T>> {
    try {
      const response = await this.executeRequest<T>({
        method: 'get',
        path: `${dto.parentModule}/${dto.externalId}/${dto.module}?fields=${dto.fields}`,
      });
      if (response === '') {
        return Result.ok({ data: [] } as T);
      }

      return Result.ok(response);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(
        `[ZohoRecordService] Error requesting related list for module ${dto.module} in Zoho:`,
        JSON.stringify(error)
      );

      return Result.fail<T>(
        `Error requesting related list for module ${
          dto.module
        } in Zoho: ${JSON.stringify(error)}`
      );
    }
  }

  public async getRecordsSortedByUpdateDate<T extends string>(params: {
    order: 'ASC' | 'DESC';
    limit: number;
    module?: string;
    field?: string;
    whereCondition?: string;
    isCacheEnabled?: boolean;
  }): Promise<Result<{ [key in T]: string }[]>> {
    try {
      const allData: { [key in T]: string }[] = [];
      const module = params.module || 'Cobros_Judiciales';
      const field = params.field || 'Name';
      const whereCondition = params.whereCondition || `${field} != null`;
      // eslint-disable-next-line no-console
      console.log(
        `[ZohoRecordService] Getting ${params.limit} unique records from ${module} ordered by ltima_actualizaci_n ${params.order}`
      );
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const select_query = `SELECT DISTINCT ${field} FROM ${module} WHERE ${whereCondition} ORDER BY ltima_actualizaci_n ${params.order} LIMIT ${params.limit}`;
      // eslint-disable-next-line no-console
      console.log(`[ZohoRecordService] Executing query: ${select_query}`);
      const response = await this.executeRequest<{
        data: { [key in T]: string }[];
        info?: { more_records?: boolean };
      }>({
        method: 'post',
        path: 'coql',
        data: { select_query },
        isCacheEnabled: params.isCacheEnabled || false,
      });
      const records = response.data || [];
      allData.push(...records);
      // eslint-disable-next-line no-console
      console.log(
        `[ZohoRecordService] Retrieved ${records.length} records, total so far: ${allData.length}`
      );

      return Result.ok(allData);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(
        `[ZohoRecordService] Error listing field "Name" from module Cobros_Judiciales:`,
        JSON.stringify(error)
      );

      return Result.fail(
        `Error listing field "Name" from module Cobros_Judiciales: ${JSON.stringify(
          error
        )}`
      );
    }
  }
}
