import { Result } from '../../../../core/Result';
import {
  CreateRecordDTO,
  CreateRecordResponseDTO,
  GetRelatedListDTO,
  SearchByIdDTO,
  SearchByIdResponseDTO,
  UpdateRecordDTO,
} from './type';

// eslint-disable-next-line @typescript-eslint/naming-convention
export interface RecordExternalStorageService {
  searchForExternalId(
    dto: SearchByIdDTO
  ): Promise<Result<SearchByIdResponseDTO>>;
  create: (dto: CreateRecordDTO) => Promise<Result<CreateRecordResponseDTO>>;
  update: (dto: UpdateRecordDTO) => Promise<Result<CreateRecordResponseDTO>>;
  getRelatedList: <T>(dto: GetRelatedListDTO) => Promise<Result<T>>;
}
