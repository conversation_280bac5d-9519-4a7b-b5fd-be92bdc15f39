import { AxiosResponse } from 'axios';

// eslint-disable-next-line @typescript-eslint/naming-convention
export type REQUEST_METHOD = 'get' | 'post' | 'put' | 'delete';
export type RequestConfig = {
  type: 'DATA' | 'FILE';
  method: REQUEST_METHOD;
  headers?: Record<string, string>;
  recordId: string;
  module: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data?: any;
  isCacheEnabled?: boolean;
  checkResponse?: boolean;
};
// eslint-disable-next-line @typescript-eslint/naming-convention
export interface ExternalRecordService {
  requestRecordData: <T>(dto: RequestRecordDataDTO) => Promise<T>;
  requestRecordDocument: <T>(
    dto: RequestRecordDataDTO
  ) => Promise<AxiosResponse<T>>;
}
export type RequestRecordDataDTO = {
  type: 'DATA' | 'FILE';
  recordId: string;
  module: string;
  subModule?: string;
  data?: object;
  headers?: Record<string, string>;
};
export type RequestRecordDataResponseDTO = {
  valorDevuelto: Record<string, unknown>;
};
export type RequestRecordDataArrayResponseDTO = {
  valorDevuelto: Record<string, unknown>[];
};
export type JudiciaryAuthDataDTO = {
  token: string;
  context: string;
};
export type JudiciaryDataValuesResponse = {
  valorDevuelto: Record<string, string>;
};
