import Axios, { AxiosInstance } from 'axios';
import { AxiosCacheInstance, setupCache } from 'axios-cache-interceptor';
import { RequestConfig } from './types';
import { AuthenticationDataMiddleware } from '../../../modules/authentication-data/middleware/authentication-data-middleware';
import { AuthenticationError } from '../../domain/errors/domain/authentication-error';
import { ExternalRecordRequestError } from '../../domain/errors/domain/external-record-request-error';
import { DomainError } from '../../domain/errors/domain/domain-error';
import { RetryWithRefreshError } from '../../../modules/record/domain/error/retry-with-refresh-error';

export class JudiciaryAPIService {
  private authenticationMiddleware: AuthenticationDataMiddleware;

  private readonly serviceBaseURL =
    'https://pjenlinea.poder-judicial.go.cr/GestionEnLinea/api/';

  private readonly fileServiceURL =
    'https://aptpj.poder-judicial.go.cr/cpglda/api/DescargaArchivoController/DescargarArchivo';

  private token: string;

  private context: string;

  private username: string;

  private recordId: string;

  private maxAuthRetries = 10;

  private scrapperRetryCounter = 0;

  private hasTriedRefreshingCurrentCredential = false;

  private axiosCacheInstance: AxiosCacheInstance;

  private axiosInstance: AxiosInstance;

  constructor({ middleware }: { middleware: AuthenticationDataMiddleware }) {
    this.authenticationMiddleware = middleware;
    this.axiosInstance = Axios.create({
      baseURL: this.serviceBaseURL,
    });
    this.axiosCacheInstance = setupCache(
      Axios.create({
        baseURL: this.serviceBaseURL,
      }),
      {
        ttl: 1000 * 60 * 5,
        methods: ['get', 'post'],
        interpretHeader: false,
        debug:
          process.env.STAGE === 'dev'
            ? (cache) => {
                // eslint-disable-next-line no-console
                console.log('[JudiciaryAPIService] Cache status:', {
                  ...cache,
                  timestamp: new Date().toISOString(),
                });
              }
            : undefined,
      }
    );
  }

  private async refreshAuthData(
    recordId: string,
    forceNewCredential = false,
    refreshCurrentCredential = false
  ): Promise<void> {
    this.recordId = recordId;
    const authDataOrError =
      await this.authenticationMiddleware.getServiceAuthData(
        recordId,
        forceNewCredential,
        refreshCurrentCredential
      );
    if (authDataOrError.isFailure) {
      // eslint-disable-next-line no-console
      console.error(
        '[JudiciaryAPIService] Failed to generate authentication data for request to API from Poder Judicial:',
        JSON.stringify(authDataOrError.getErrorValue())
      );
      throw new Error(
        `Failed to generate authentication data for request to API from Poder Judicial: ${
          typeof authDataOrError.getErrorValue() === 'string'
            ? authDataOrError.getErrorValue()
            : JSON.stringify(authDataOrError.getErrorValue())
        }`
      );
    }
    this.token = authDataOrError.getValue().token;
    this.context = authDataOrError.getValue().context;
    this.username = authDataOrError.getValue().username;
  }

  private async refreshCurrentCredential(): Promise<void> {
    if (!this.recordId) {
      throw new Error('RecordId not available for credential refresh');
    }
    // eslint-disable-next-line no-console
    console.log('[JudiciaryAPIService] Refreshing current credential');
    await this.refreshAuthData(this.recordId, false, true);
  }

  private async requestAuthDataForNextUsername(): Promise<void> {
    if (!this.recordId) {
      throw new Error('RecordId not available for credential rotation');
    }
    // eslint-disable-next-line no-console
    console.log('[JudiciaryAPIService] Requesting next credential');
    await this.refreshAuthData(this.recordId, true, false);
  }

  public executeRequest = async <T>(dto: RequestConfig): Promise<T> => {
    let retryCount = 0;
    this.recordId = dto.recordId;
    this.hasTriedRefreshingCurrentCredential = false;
    // eslint-disable-next-line no-constant-condition
    while (true) {
      const config = await this.getRequestConfig(dto);
      const axios = this.getAxiosInstance(dto.isCacheEnabled);
      try {
        const response = await axios({
          ...config,
          cache: {
            cacheTakeover: true,
            methods: ['get'],
          },
          responseType: dto.type === 'FILE' ? 'arraybuffer' : undefined,
        });
        // eslint-disable-next-line no-console
        console.log('[JudiciaryAPIService] response:', response);
        if (dto.checkResponse === false) {
          if (dto.type === 'FILE') return response as T;

          return response.data as T;
        }
        try {
          return await this.checkResponse<T>(response.data);
        } catch (error) {
          if (error instanceof RetryWithRefreshError) {
            this.scrapperRetryCounter++;
            // eslint-disable-next-line no-console
            console.log(
              `[JudiciaryAPIService] Increasing scrapper retry counter to: ${this.scrapperRetryCounter}`
            );
            await this.refreshCurrentCredential();
            continue;
          }
          throw error;
        }
      } catch (error: unknown) {
        // eslint-disable-next-line no-console
        console.error('[JudiciaryAPIService] Error in request:', error);
        if (error instanceof DomainError) {
          throw error;
        }
        if (Axios.isAxiosError(error) && error.response?.status === 401) {
          if (!this.hasTriedRefreshingCurrentCredential) {
            // eslint-disable-next-line no-console
            console.log(
              '[JudiciaryAPIService] Token expired or invalid, trying to refresh current credential...'
            );
            this.hasTriedRefreshingCurrentCredential = true;
            try {
              await this.refreshCurrentCredential();
              continue;
            } catch (refreshError) {
              // eslint-disable-next-line no-console
              console.log(
                '[JudiciaryAPIService] Failed to refresh current credential:',
                refreshError,
                'Will try next credential...'
              );
            }
          }
          retryCount++;
          // eslint-disable-next-line no-console
          console.warn(
            `[JudiciaryAPIService] Authentication failed with Poder Judicial: ${error.message}. Retry counter on ${retryCount} Attempting to retry with next credential...`
          );
          if (retryCount > this.maxAuthRetries) {
            // eslint-disable-next-line no-console
            console.warn(
              `[JudiciaryAPIService] No more chance to retry, counter on ${retryCount}`
            );
            throw new AuthenticationError(
              `Authentication failed after ${this.maxAuthRetries} attempts with current credentials`,
              dto.recordId,
              {
                maxAuthRetries: this.maxAuthRetries,
                retryCount,
                date: new Date().toISOString(),
              }
            );
          }
          // eslint-disable-next-line no-console
          console.log(
            `[JudiciaryAPIService] Trying with next credential (attempt ${retryCount}/${this.maxAuthRetries})...`
          );
          await this.requestAuthDataForNextUsername();
          this.hasTriedRefreshingCurrentCredential = false;
          continue;
        }
        // eslint-disable-next-line no-console
        console.log(
          `[JudiciaryAPIService] Unexpected error while executing API request to "${
            dto.module
          }" to Poder Judicial: ${
            error instanceof Error ? error.message : JSON.stringify(error)
          }`,
          JSON.stringify(error)
        );
        throw new ExternalRecordRequestError(
          `Unexpected error while executing API request to "${
            dto.module
          }" to Poder Judicial: ${
            error instanceof Error ? error.message : JSON.stringify(error)
          }`,
          dto.recordId,
          {
            requestData: dto.data,
            date: new Date().toISOString(),
          }
        );
      }
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private getRequestConfig = async (config: RequestConfig): Promise<any> => {
    if (!this.token || !this.context) {
      await this.refreshAuthData(config.recordId);
    }
    const baseURL =
      config.type === 'FILE' ? this.fileServiceURL : this.serviceBaseURL;
    const requestConfig = {
      baseURL,
      method: config.method,
      url: config.module,
      headers: {
        Authorization: `${this.token}`,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'Content-Type': 'application/json',
        Accept: 'application/json, text',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        sec_ch_sucontentsys: this.context,
        Referer:
          'https://pjenlinea.poder-judicial.go.cr/GestionEnLinea/inicio/consultas/consultanumeroexpediente',
        Origin: 'https://pjenlinea.poder-judicial.go.cr',
        Host:
          config.type === 'FILE' ? undefined : 'pjenlinea.poder-judicial.go.cr',
        ...config.headers,
      },
      data: {
        puestoTrabajoFuncionario: {
          funcionario: {
            usuarioRed: this.username,
          },
        },
        // TODO : construir implementacion dinamica para la comprobacion de la necesidad de enviar funcionario dentro o fuero del puesto de trabajo
        funcionario: {
          usuarioRed: this.username,
        },
        ...config.data,
      },
    };
    // eslint-disable-next-line no-console
    console.log(
      '[JudiciaryAPIService] request  API config:',
      JSON.stringify(requestConfig)
    );

    return requestConfig;
  };

  private getAxiosInstance(isCacheEnabled?: boolean) {
    return isCacheEnabled ? this.axiosCacheInstance : this.axiosInstance;
  }

  private async checkResponse<T>(data: unknown): Promise<T> {
    // eslint-disable-next-line no-console
    console.log(
      '[JudiciaryAPIService][CheckResponse] Checking API Response:',
      data
    );
    if (typeof data !== 'object' || data === null) {
      // eslint-disable-next-line no-console
      console.error(
        '[JudiciaryAPIService][CheckResponse] Invalid response format from judiciary API'
      );
      throw new Error('Invalid response format from judiciary API');
    }
    const response = data as Record<string, unknown>;
    if (response.huboError === undefined) {
      // eslint-disable-next-line no-console
      console.error(
        '[JudiciaryAPIService][CheckResponse] Response does not have huboError property'
      );
      throw new Error('Response does not have huboError property');
    }
    if (response.huboError === true) {
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryAPIService][CheckResponse] Judiciary Response is Error:',
        JSON.stringify(response)
      );
      if (
        typeof response.mensajeUsuario === 'string' &&
        response.mensajeUsuario.includes('401')
      ) {
        // eslint-disable-next-line no-console
        console.error(
          `[JudiciaryAPIService][CheckResponse] Judiciary Response is 401 with retry counter ${this.scrapperRetryCounter}`
        );
        if (this.scrapperRetryCounter <= 10) {
          throw new RetryWithRefreshError('Need to refresh token and retry');
        } else {
          // eslint-disable-next-line no-console
          console.error(
            `[JudiciaryAPIService][CheckResponse] Response is 401 and retry counter is ${this.scrapperRetryCounter}, throwing error`
          );
          throw new AuthenticationError(
            `Authentication failed with Poder Judicial: ${response.mensajeUsuario}`,
            this.recordId,
            {
              maxAuthRetries: this.maxAuthRetries,
              response: data,
              date: new Date().toISOString(),
            }
          );
        }
      } else {
        throw new ExternalRecordRequestError(
          `Error from Judiciary API: ${
            response.mensajeUsuario || 'Unknown error'
          }`,
          this.recordId,
          { response: data, date: new Date().toISOString() }
        );
      }
    }
    // eslint-disable-next-line no-console
    console.log(
      '[JudiciaryAPIService][CheckResponse] Response is Correct:',
      response
    );

    return data as T;
  }
}
