/* eslint-disable no-console */
import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer-core';
import chromium from '@sparticuz/chromium';
import { ScraperService } from '../scrapper-service';
import { JudiciaryAuthDataDTO } from './types';
import { Result } from '../../core/Result';
import { GetTokenAndContextDTO } from '../../../modules/authentication-data/dto/authentication-data-dto';

export class PuppeteerScraperService implements ScraperService {
  private page: Page;

  private browser: Browser;

  private authData: {
    token: string | null;
    context: string | null;
  } = {
    token: null,
    context: null,
  };

  public async getTokenAndContext(
    dto: GetTokenAndContextDTO
  ): Promise<Result<JudiciaryAuthDataDTO>> {
    console.log(
      '[SCRAPPER] Starting PuppeteerScraperService with:',
      dto,
      '...'
    );
    const responseOrError = await this.init();
    if (responseOrError.isFailure) {
      return Result.fail<JudiciaryAuthDataDTO>(responseOrError.getErrorValue());
    }
    try {
      const { token, context } = await this.executeLoginFlow(dto);

      return Result.ok<JudiciaryAuthDataDTO>({
        token,
        context,
      });
    } catch (error) {
      console.error(
        `Error getting token and context using credentials for user ${
          dto.username
        }: ${error instanceof Error ? error.message : JSON.stringify(error)}`
      );

      return Result.fail<JudiciaryAuthDataDTO>(
        `Error getting token and context using credentials for user ${
          dto.username
        }: ${error instanceof Error ? error.message : JSON.stringify(error)}`
      );
    } finally {
      if (this.browser) await this.browser.close();
    }
  }

  private async init(): Promise<Result<string>> {
    console.log('[SCRAPPER] Initializing browser...');
    try {
      const executablePath = await chromium.executablePath();
      if (!executablePath) {
        throw new Error('Chromium executable path not found');
      }
      console.log('[SCRAPPER] Chrome executable path:', executablePath);
      this.browser = await puppeteer.launch({
        args: [
          ...chromium.args,
          '--no-sandbox',
          '--disable-dev-shm-usage',
          '--disable-gpu',
          '--single-process',
          '--disable-setuid-sandbox',
        ],
        executablePath: executablePath,
        headless: true,
        defaultViewport: chromium.defaultViewport,
      });
      console.log('[SCRAPPER] Browser initialized.');
      console.log('[SCRAPPER] Creating new page...');
      this.page = await this.browser.newPage();
      await this.page.setViewport({ width: 1366, height: 768 });
      await this.page.setUserAgent(
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      );
      this.page.setDefaultTimeout(200000);
      console.log('[SCRAPPER] Page created.');
      await this.setupRequestInterceptors();

      return Result.ok();
    } catch (error) {
      console.error(`Error initializing browser: ${error}`);

      return Result.fail<string>(`Error initializing browser: ${error}`);
    }
  }

  private async executeLoginFlow(dto: GetTokenAndContextDTO): Promise<{
    token: string;
    context: string;
  }> {
    try {
      console.log('[SCRAPPER] Executing login flow...');
      const LOGIN_URL =
        'https://pjenlinea.poder-judicial.go.cr/GestionEnLinea/inicio/tramitesfrecuentes#googtrans(es%7Ces)';
      console.log('[SCRAPPER] Going to login page...');
      await this.page.goto(LOGIN_URL, { waitUntil: 'networkidle0' });
      console.log('[SCRAPPER] Page redirected.');
      console.log('[SCRAPPER] Waiting for page to load...');
      await this.waitPageToLoad();
      console.log('[SCRAPPER] Page loaded.');
      await this.authenticate(dto);
      await this.executeLogin();
      await this.clickContinueButtonIfPresent();
      await this.wait(3000);
      if (!this.authData.token || !this.authData.context) {
        throw new Error('Token or context not found');
      }
      console.log('[SCRAPPER] Success getting token and context', {
        token: this.authData.token,
        context: this.authData.context,
      });

      return {
        token: this.authData.token,
        context: this.authData.context,
      };
    } catch (error) {
      console.error(
        `Error executing login flow: ${
          error instanceof Error ? error.message : JSON.stringify(error)
        }`
      );
      throw error;
    }
  }

  private async setupRequestInterceptors(): Promise<void> {
    if (!this.page) {
      throw new Error('Page not initialized');
    }
    await this.page.setRequestInterception(true);
    this.page.on('request', (request) => {
      const url = request.url();
      if (
        url.startsWith(
          'https://pjenlinea.poder-judicial.go.cr/GestionEnLinea/api'
        )
      ) {
        const headers = request.headers();
        const token = headers['authorization'];
        const context = headers['sec_ch_sucontentsys'];
        if (token) this.authData.token = token;
        if (context) this.authData.context = context;
        if (token && context) {
          console.log('[SCRAPPER] Authentication Data:', {
            authorization: this.authData.token,
            context: this.authData.context,
          });
        }
      }
      request.continue();
    });
  }

  private async authenticate(dto: GetTokenAndContextDTO) {
    console.log('[SCRAPPER] Authenticating...');
    await this.waitPageToLoad();
    const userField = await this.page.$('input[placeholder="Usuario"]');
    if (userField) {
      console.log('[SCRAPPER] User field found.');
      await userField.click();
      await userField.type(dto.username);
    }
    const passwordField = await this.page.$('input[placeholder="Contraseña"]');
    if (passwordField) {
      console.log('[SCRAPPER] Password field found.');
      await passwordField.click();
      await passwordField.type(dto.password);
    }
    console.log('[SCRAPPER] Authenticated.');
    await this.waitPageToLoad();
  }

  private async executeLogin() {
    await this.waitPageToLoad();
    console.log('[SCRAPPER] Logging in...');
    await this.wait(2000);
    const [loginButton] = await this.page.$x(
      '//button[contains(text(), "Ingresar")]'
    );
    if (loginButton) {
      console.log('[SCRAPPER] Login button found.');
      await loginButton.click();
      await this.waitPageToLoad();
      await this.wait(2000);
    } else {
      console.log('[SCRAPPER] Login button not found.');
      throw new Error('Login button not found');
    }
    await this.waitPageToLoad();
    await this.wait(2000);
    console.log('[SCRAPPER] Logged in.');
  }

  private async clickContinueButtonIfPresent() {
    console.log('[SCRAPPER] Clicking continue button if present...');
    await this.wait(5000);
    const [button] = await this.page.$x(
      '//button[contains(text(), "Continuar")]'
    );
    let isVisible = false;
    if (button) {
      console.log('[SCRAPPER] Continue button found.');
      const boundingBox = await button.boundingBox();
      console.log('[SCRAPPER] Continue button bounding box:', boundingBox);
      isVisible = boundingBox !== null;
    } else {
      console.log('[SCRAPPER] Continue button not found.');
    }
    if (isVisible) {
      console.log('[SCRAPPER] Clicking continue button...');
      await button.click();
      await this.wait(5000);
      console.log(`[SCRAPPER] Continue button clicked.`);
    } else {
      await this.wait(2000);
    }
  }

  private async waitPageToLoad(timeout = 10000) {
    try {
      //TODO: improve wait for navigation logic since it's not working as expected and its always timing out
      await this.page
        .waitForNavigation({
          waitUntil: ['domcontentloaded', 'load', 'networkidle0'],
          timeout,
        })
        .catch(() => {
          console.log('[SCRAPPER] Navigation timeout, continuing anyway');
        });
      await this.page.waitForFunction('document.readyState === "complete"', {
        timeout,
      });
    } catch (error) {
      console.warn(`[SCRAPPER] Page load timeout: ${error}`);
    }
  }

  private async wait(milliseconds: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, milliseconds));
  }
}
