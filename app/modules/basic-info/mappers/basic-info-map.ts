import { Result } from '../../../shared/core/Result';
import { BasicInfoDTO } from '../dto/basic-info-dto';
import { BasicInfo } from '../domain/BasicInfo';
import { SimpleEmptyTextValueObject } from '../../../shared/common-value-objects/simple-empty-text-value-object';
import { ExternalRecordBasicInfoDTO } from '../dto/external-record-dto';

export class BasicInfoMap {
  public static toDTO(basicInfo: BasicInfo): BasicInfoDTO {
    return {
      status: basicInfo.status.value,
      entryDate: basicInfo.entryDate.value,
      statusDate: basicInfo.statusDate.value,
      verifiedDigit: basicInfo.verifiedDigit.value,
      class: basicInfo.class.value,
      office: basicInfo.office.value,
      decidingJudge: basicInfo.decidingJudge.value,
      processingJudge: basicInfo.processingJudge.value,
      description: basicInfo.description.value,
      location: basicInfo.location.value,
      context: basicInfo.context,
      folder: basicInfo.folder,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static toDomain(raw: any): BasicInfo {
    const statusOrError = SimpleEmptyTextValueObject.create(
      'status',
      raw.status
    );
    const entryDateOrError = SimpleEmptyTextValueObject.create(
      'entryDate',
      raw.entryDate
    );
    const statusDateOrError = SimpleEmptyTextValueObject.create(
      'statusDate',
      raw.statusDate
    );
    const verifiedDigitOrError = SimpleEmptyTextValueObject.create(
      'verifiedDigit',
      raw.verifiedDigit
    );
    const classOrError = SimpleEmptyTextValueObject.create('class', raw.class);
    const officeOrError = SimpleEmptyTextValueObject.create(
      'office',
      raw.office
    );
    const decidingJudgeOrError = SimpleEmptyTextValueObject.create(
      'decidingJudge',
      raw.decidingJudge
    );
    const processingJudgeOrError = SimpleEmptyTextValueObject.create(
      'processingJudge',
      raw.processingJudge
    );
    const descriptionOrError = SimpleEmptyTextValueObject.create(
      'description',
      raw.description
    );
    const locationOrError = SimpleEmptyTextValueObject.create(
      'location',
      raw.location
    );
    const dtoCombine = Result.combine([
      statusOrError,
      entryDateOrError,
      statusDateOrError,
      verifiedDigitOrError,
      classOrError,
      officeOrError,
      decidingJudgeOrError,
      processingJudgeOrError,
      descriptionOrError,
      locationOrError,
    ]);
    if (dtoCombine.isFailure) {
      throw dtoCombine.getErrorValue();
    }
    const notification = BasicInfo.create({
      status: statusOrError.getValue(),
      entryDate: entryDateOrError.getValue(),
      statusDate: statusDateOrError.getValue(),
      verifiedDigit: verifiedDigitOrError.getValue(),
      class: classOrError.getValue(),
      office: officeOrError.getValue(),
      decidingJudge: decidingJudgeOrError.getValue(),
      processingJudge: processingJudgeOrError.getValue(),
      description: descriptionOrError.getValue(),
      location: locationOrError.getValue(),
      context: raw.context,
      folder: raw.folder,
    });
    if (notification.isFailure) throw notification.getErrorValue();

    return notification.getValue();
  }

  // eslint-disable-next-line
  public static toPersistence(basicInfo: BasicInfo): any {
    return {
      status: basicInfo.status.value,
      entryDate: basicInfo.entryDate.value,
      statusDate: basicInfo.statusDate.value,
      verifiedDigit: basicInfo.verifiedDigit.value,
      class: basicInfo.class.value,
      office: basicInfo.office.value,
      decidingJudge: basicInfo.decidingJudge.value,
      processingJudge: basicInfo.processingJudge.value,
      description: basicInfo.description.value,
      location: basicInfo.location.value,
      context: basicInfo.context,
      folder: basicInfo.folder,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public static fromJudiciaryAPIToDomain(raw: any): Result<BasicInfo> {
    try {
      const mappedData = {
        status: raw.estado?.descripcion || '',
        entryDate: raw.fechaEntrada || '',
        statusDate: raw.estado?.fechaActivacion || '',
        verifiedDigit: raw.digitoVerificador || '',
        class: raw.clase?.descripcion || '',
        office: raw.contexto?.oficina?.descripcion || '',
        decidingJudge: '',
        processingJudge: '',
        description: raw.descripcion || '',
        location: raw.historicoUbicacion?.ubicacion?.descripcion || '',
        context: raw.contexto || {},
        folder: raw.carpeta || '',
      };
      const basicInfo = this.toDomain(mappedData);

      return Result.ok<BasicInfo>(basicInfo);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[BasicInfoMap] Error mapping BasicInfo judiciary API response:',
        error
      );

      return Result.fail<BasicInfo>(
        `Failed to map BasicInfo judiciary API response: ${error}`
      );
    }
  }

  public static toExternalPersistence(
    recordId: string,
    basicInfo: BasicInfo
  ): ExternalRecordBasicInfoDTO {
    const [year, consecutive, judged, subject] = recordId.split('-');

    return {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      Estado: basicInfo.status.value || '-',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      Fecha_Estado: basicInfo.statusDate.value.split('T')[0] || '-',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      D_gito_verificador: basicInfo.verifiedDigit.value || '-',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      N_mero_de_Expediente: recordId || '-',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      Descripci_n: basicInfo.description.value || '-',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      ltima_actualizaci_n: this.getCostaRicaFormattedTime(),
      // TODO: Update this to use the correct field from the record
      // eslint-disable-next-line @typescript-eslint/naming-convention
      Estado_de_actualizaci_n: 'Actualizado',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      A_o_del_Expediente: year.substring(year.length - 2),
      // eslint-disable-next-line @typescript-eslint/naming-convention
      Consecutivo_del_Expediente: consecutive || '-',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      Juzgado_del_Expediente: judged || '-',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      Materia_del_Expediente: subject || '-',
      Name: recordId || '-',
      Clase: basicInfo.class.value || '-',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      Oficina_Judicial: basicInfo.office.value || '-',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      Ubicaci_n: basicInfo.location.value || '-',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      Fecha_de_Entrada: basicInfo.entryDate.value.split('T')[0],
      // eslint-disable-next-line @typescript-eslint/naming-convention
      Juez_tramitador: basicInfo.processingJudge.value || '-',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      Juez_Decisor: basicInfo.decidingJudge.value || '-',
    };
  }

  private static getCostaRicaFormattedTime = (date?: string): string => {
    // Create a date object for the current time
    const now = date ? new Date(date) : new Date();
    // Costa Rica is in UTC-6 timezone (no DST)
    const CR_TIMEZONE_OFFSET = -6 * 60; // -6 hours in minutes
    // Set the timezone offset manually to Costa Rica's timezone
    const userTimezoneOffset = now.getTimezoneOffset();
    // Calculate the time difference to adjust for Costa Rica time
    const timezoneDifference = userTimezoneOffset - CR_TIMEZONE_OFFSET;
    // Create a new date object with the adjusted time
    const costaRicaTime = new Date(
      now.getTime() + timezoneDifference * 60 * 1000
    );
    // Format the date to match the required pattern
    const year = costaRicaTime.getFullYear();
    const month = String(costaRicaTime.getMonth() + 1).padStart(2, '0');
    const day = String(costaRicaTime.getDate()).padStart(2, '0');
    const hours = String(costaRicaTime.getHours()).padStart(2, '0');
    const minutes = String(costaRicaTime.getMinutes()).padStart(2, '0');
    const seconds = String(costaRicaTime.getSeconds()).padStart(2, '0');

    // Create the formatted string with the Costa Rica timezone offset
    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}-06:00`;
  };
}
