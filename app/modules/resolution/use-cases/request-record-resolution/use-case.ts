import { DomainError } from '../../../../shared/domain/errors/domain/domain-error';
import { ExternalServiceError } from '../../../../shared/domain/errors/domain/external-service-error';
import { ValidationError } from '../../../../shared/domain/errors/domain/validation-error';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';
import {
  ExternalRecordService,
  RequestRecordDataArrayResponseDTO,
  RequestRecordDataResponseDTO,
} from '../../../../shared/services/judiciary/types';
import { RecordId } from '../../../record/domain/record-id';
import { Resolution } from '../../domain/Resolution';
import { ResolutionMap } from '../../mappers/resolution-map';
import { RequestRecordResolutionsDTO } from './dto';

export class RequestRecordResolutionsUseCase {
  private recordId: string;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private context: any;

  private folder: string | undefined;

  constructor(private externalRecordService: ExternalRecordService) {}

  @LogUseCaseDTO public async execute(
    dto: RequestRecordResolutionsDTO
  ): Promise<Resolution[] | DomainError> {
    try {
      this.recordId = this.mapDTOtoRecordId(dto);
      this.context = dto.context;
      this.folder = dto.folder;
      const partialResult = await this.gerRecordDataFromJudiciary();

      return await this.hydrateResolutions(partialResult);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[RequestRecordResolutionsUseCase] Error executing use case:',
        JSON.stringify(error)
      );
      if (error instanceof DomainError) {
        // eslint-disable-next-line no-console
        console.log('[RequestRecordResolutionsUseCase] DomainError:', error);

        return error;
      }

      return new ExternalServiceError(
        `[RequestRecordResolutionsUseCase] Error executing use case: ${
          error instanceof Error ? error.message : 'An unknown error occurred'
        }`,
        dto.recordId,
        'RequestRecordResolutionsUseCase => execute',
        {
          module: 'Resolutions',
          originalError: JSON.stringify(error),
          requestData: dto,
        }
      );
    }
  }

  private mapDTOtoRecordId(dto: RequestRecordResolutionsDTO) {
    try {
      const recordIdOrError = RecordId.create(dto.recordId);
      if (recordIdOrError.isFailure) {
        throw recordIdOrError.getErrorValue();
      }

      return recordIdOrError.getValue().value;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[RequestRecordResolutionsUseCase] Error mapping DTO to Record ID:',
        JSON.stringify(error)
      );
      throw new ValidationError(
        `[RequestRecordResolutionsUseCase] Failed to map DTO to Record ID: ${
          error instanceof Error ? error.message : 'An unknown error occurred'
        }`,
        'INVALID_RECORD_ID',
        this.recordId,
        { recordId: dto.recordId, originalError: error }
      );
    }
  }

  private async gerRecordDataFromJudiciary() {
    // eslint-disable-next-line no-console
    console.log(
      '[RequestRecordResolutionsUseCase] Getting Record Resolutions....'
    );
    const resolutions =
      await this.externalRecordService.requestRecordData<RequestRecordDataArrayResponseDTO>(
        {
          module: 'DocumentoController/ConsultarResoluciones',
          type: 'DATA',
          recordId: this.recordId,
          data: {
            expediente: {
              numero: this.recordId.replace(/-/g, ''),
              ...(this.folder ? { carpeta: this.folder } : {}),
              contexto: {
                provieneSIAGPJ: false,
                consultaPublicaCiudadano: false,
                consultaPrivadaCiudadano: true,
                ...this.context,
              },
            },
            contexto: {
              provieneSIAGPJ: false,
              consultaPublicaCiudadano: false,
              consultaPrivadaCiudadano: true,
              ...this.context,
            },
          },
        }
      );

    return resolutions.valorDevuelto || [];
  }

  private async hydrateResolutions(
    result: Record<string, unknown>[]
  ): Promise<Resolution[]> {
    const hydratedResolutions: Resolution[] = [];
    for (const resolution of result) {
      const { valorDevuelto: resolutionDetail } =
        await this.gerRecordDetailFromJudiciary(resolution);
      const resolutionDomain = this.mapJudiciaryRecordToDomain(
        resolution,
        resolutionDetail
      );
      hydratedResolutions.push(resolutionDomain);
    }

    return hydratedResolutions;
  }

  private async gerRecordDetailFromJudiciary(parentContext: unknown) {
    // eslint-disable-next-line no-console
    console.log(
      '[RequestRecordResolutionsUseCase] Getting Record Resolution Detail with context:',
      JSON.stringify(parentContext)
    );
    const resolutions =
      await this.externalRecordService.requestRecordData<RequestRecordDataResponseDTO>(
        {
          module: 'DocumentoController/ConsultarDetalleResolucion',
          type: 'DATA',
          recordId: this.recordId,
          data: {
            ...(parentContext as Record<string, unknown>),
            contexto: {
              ...this.context,
            },
            expediente: {
              ...(this.folder ? { carpeta: this.folder } : {}),
            },
          },
        }
      );

    return resolutions;
  }

  private mapJudiciaryRecordToDomain(
    judiciaryRecord: Record<string, unknown>,
    judiciaryRecordDetail: Record<string, unknown>
  ): Resolution {
    const domainOrError = ResolutionMap.fromJudiciaryAPIToDomain(
      judiciaryRecord,
      judiciaryRecordDetail
    );
    if (domainOrError.isFailure) {
      // eslint-disable-next-line no-console
      console.error(
        '[RequestRecordResolutionsUseCase] Map Judiciary Record to Domain Error:',
        domainOrError.getErrorValue()
      );
      throw domainOrError.getErrorValue();
    }

    return domainOrError.getValue();
  }
}
