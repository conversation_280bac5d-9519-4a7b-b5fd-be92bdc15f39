import { UseCase } from '../../../../shared/core/use-case';
import { DatabaseError } from '../../../../shared/domain/errors/domain/database-error';
import {
  ErrorCategory,
  SerializedError,
} from '../../../../shared/domain/errors/domain/domain-error';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';
import { EmailService } from '../../../../shared/services/email-service';
import { RecordExternalStorageService } from '../../../../shared/services/zoho/crm/record/record-external-storage-service';
import { ErrorNotificationDTO } from './dto';

const TECH_SUPPORT_EMAIL = '<EMAIL>';
export class ErrorNotificationUseCase
  implements UseCase<ErrorNotificationDTO, Promise<void>>
{
  private recordId: string;

  constructor(
    private emailService: EmailService,
    private recordExternalStorageService: RecordExternalStorageService
  ) {}

  @LogUseCaseDTO
  public async execute(dto: ErrorNotificationDTO): Promise<void> {
    const { error, userEmail } = dto;
    this.recordId = error.recordId;
    await Promise.all([
      this.updateRecordStatus(),
      this.sendErrorEmail(error, userEmail),
    ]);
  }

  private async sendErrorEmail(
    error: SerializedError,
    userEmail?: string
  ): Promise<void> {
    try {
      const subject = this.getEmailSubject(error);
      const body = this.getEmailBody(error);
      await this.emailService.sendEmail({
        subject,
        body,
        recipients: this.getRecipientsForErrorCategory(
          error.category,
          userEmail
        ),
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error sending email';
      // eslint-disable-next-line no-console
      console.error('Failed to send error email:', {
        error: errorMessage,
      });
    }
  }

  private getEmailSubject(error: SerializedError): string {
    const recordId = error.recordId;
    switch (error.category) {
      case ErrorCategory.AUTHENTICATION || ErrorCategory.AUTHORIZATION:
        return `Error: Fallo en autenticación de credenciales de ingreso al Poder Judicial - Expediente: ${recordId}`;
      case ErrorCategory.EXTERNAL_SERVICE ||
        ErrorCategory.PJ_EXTERNAL_SERVICE ||
        ErrorCategory.TIMEOUT:
        return `Error: Falla temporal en la conexión al Poder Judicial - Expediente: ${recordId}]`;
      default:
        return `Error inesperado al procesar expediente - Expediente: ${recordId}`;
    }
  }

  private getEmailBody(error: SerializedError): string {
    const recordId = error.recordId;
    const footer = this.getEmailFooter(error);
    switch (error.category) {
      case ErrorCategory.AUTHENTICATION || ErrorCategory.AUTHORIZATION:
        return `
        <h2>Fallo en autenticación de Credenciales</h2>
        <h3>Expediente: ${recordId}</h3>
        <p>Por favor ingresar al sistema y verificar que:</p>
        <ul>
          <li>El expediente tenga credenciales (usuario y clave) asignadas correctamente.</li>
          <li>Que las credenciales estén digitadas correctamente y/o estén vigentes.</li>
        </ul>  
        <p>Una vez actualizado, puede volver a intentar actualizar el expediente de manera manual o bien permitir al sistema que lo realice durante su ciclo programado de manera automática.</p>
        ${footer}`;
      case ErrorCategory.EXTERNAL_SERVICE ||
        ErrorCategory.PJ_EXTERNAL_SERVICE ||
        ErrorCategory.TIMEOUT:
        return `
        <h2>Error temporal en conexión</h2>
        <h3>Expediente: ${recordId}</h3>
        <p>En este momento no fue posible conectarse con el portal del Poder Judicial. Es probable que el servidor esté temporalmente fuera de servicio. 
        No se requiere acción de su parte, favor esperar al menos 1 hora antes de volver a actualizar el expediente de manera manual o bien permitir que sistema que lo realice durante su ciclo programado de manera automática.
        Si el problema persiste luego de 3 intentos favor reenviar este mensaje a <strong><EMAIL></strong></p>
        ${footer}`;
      case ErrorCategory.CRM_EXTERNAL_SERVICE ||
        ErrorCategory.VALIDATION ||
        ErrorCategory.INFRASTRUCTURE ||
        ErrorCategory.DATABASE ||
        ErrorCategory.UNKNOWN:
        return `
        <h2>Error inesperado al procesar expediente</h2>
        <h3>Expediente: ${recordId}</h3>
        <p>Ocurrió un error inesperado al intentar procesar el expediente. Para resolverlo, por favor reenviar este mensaje al correo <strong><EMAIL></strong>
        El equipo se pondrá en contacto con usted para resolverlo.</p>
        ${footer}`;
      default:
        return `
        <h2>Error de Sistema</h2>
        <h3>Expediente: ${recordId}</h3>
        <p><strong>Categoría:</strong> ${error.category}</p>
        <p><strong>Código:</strong> ${error.code}</p>
        <p><strong>Mensaje:</strong> ${error.message}</p>
        <p><strong>Fecha:</strong> ${error.timestamp}</p>
        ${
          error.details
            ? `<h3>Detalles:</h3><pre>${JSON.stringify(
                error.details,
                null,
                2
              )}</pre>`
            : ''
        }
      `;
    }
  }

  private getEmailFooter(error: SerializedError): string {
    return `
    <br><hr><br>
    <details>
    <summary>Detalles Técnicos</summary>
    <p><strong>Categoría:</strong> ${error.category}</p>
    <p><strong>Código:</strong> ${error.code}</p>
    <p><strong>Mensaje:</strong> ${error.message}</p>
    <p><strong>Fecha:</strong> ${error.timestamp}</p>
    <p><strong>Ambiente:</strong> ${error.stage}</p>
    ${
      error.details
        ? `<h3>Contexto:</h3><pre>${JSON.stringify(
            error.details,
            null,
            2
          )}</pre>`
        : ''
    }
    </details>
  `;
  }

  private getRecipientsForErrorCategory(
    category: ErrorCategory,
    userEmail?: string
  ): string[] {
    const recipientMap: Record<ErrorCategory, string[]> = {
      [ErrorCategory.AUTHENTICATION]: [TECH_SUPPORT_EMAIL],
      [ErrorCategory.EXTERNAL_SERVICE]: [TECH_SUPPORT_EMAIL],
      [ErrorCategory.TIMEOUT]: [TECH_SUPPORT_EMAIL],
      [ErrorCategory.UNKNOWN]: [TECH_SUPPORT_EMAIL],
      [ErrorCategory.AUTHORIZATION]: [TECH_SUPPORT_EMAIL],
      [ErrorCategory.VALIDATION]: [TECH_SUPPORT_EMAIL],
      [ErrorCategory.INFRASTRUCTURE]: [TECH_SUPPORT_EMAIL],
      [ErrorCategory.DATABASE]: [TECH_SUPPORT_EMAIL],
      [ErrorCategory.CRM_EXTERNAL_SERVICE]: [TECH_SUPPORT_EMAIL],
      [ErrorCategory.PJ_EXTERNAL_SERVICE]: [TECH_SUPPORT_EMAIL],
    };
    const recipients = recipientMap[category] || [TECH_SUPPORT_EMAIL];
    if (userEmail) {
      recipients.push(userEmail);
    }

    return recipients;
  }

  private async updateRecordStatus(): Promise<void> {
    const externalRecordId = await this.getRecordExternalId();
    const resultOrError = await this.recordExternalStorageService.update({
      module: 'Cobros_Judiciales',
      externalId: externalRecordId,
      data: {
        Name: this.recordId,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        Estado_de_actualizaci_n: 'Error',
      },
    });
    if (resultOrError.isFailure) {
      throw new DatabaseError(
        `Failed to update external record status: ${resultOrError.getErrorValue()}`,
        this.recordId,
        'updateStatus',
        {}
      );
    }
  }

  private async getRecordExternalId(): Promise<string> {
    try {
      const externalRecordOrError =
        await this.recordExternalStorageService.searchForExternalId({
          module: 'Cobros_Judiciales',
          queryParam: 'Name',
          queryValue: this.recordId,
        });
      if (externalRecordOrError.isFailure) {
        throw externalRecordOrError.getErrorValue();
      }

      return externalRecordOrError.getValue().data[0].id;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[JudiciaryRecordUpdater] Error initializing external record:',
        error
      );
      throw error;
    }
  }
}
