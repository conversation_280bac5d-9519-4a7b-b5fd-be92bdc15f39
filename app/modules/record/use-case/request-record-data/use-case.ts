import { UseCase } from '../../../../shared/core/use-case';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';
import { RequestRecordDataDTO } from './dto';
import { RecordRepository } from '../../repo/record-repository';
import { RecordId } from '../../domain/record-id';
import { EventService } from '../../../../shared/services/aws/publish-sqs-event-service';
import { RequestRecordDataUseCaseResponse } from './types';

export class RequestRecordDataUseCase
  implements
    UseCase<RequestRecordDataDTO, Promise<RequestRecordDataUseCaseResponse>>
{
  constructor(
    private repository: RecordRepository,
    private eventService: EventService
  ) {}

  @LogUseCaseDTO
  public async execute(
    dto: RequestRecordDataDTO
  ): Promise<RequestRecordDataUseCaseResponse> {
    try {
      const recordOrNull = await this.getRecordById(dto);
      if (recordOrNull && recordOrNull.isRecordUpdatedRecently()) {
        return {
          status: 'TOO_MANY_REQUESTS',
          statusCode: 429,
          message:
            'Record data was already requested recently. Please try again later.',
        };
      }
      await this.processRequest(dto.recordId, dto.userEmail);

      return {
        status: 'ACCEPTED',
        statusCode: 202,
        message: 'Record data request accepted and is being processed',
      };
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(
        '[RequestRecordDataUseCase] Error requesting record data:',
        JSON.stringify(error)
      );

      return {
        status: 'INTERNAL_SERVER_ERROR',
        statusCode: 500,
        message: 'An unexpected error occurred while processing the request',
      };
    }
  }

  private async getRecordById(dto: RequestRecordDataDTO) {
    try {
      const recordId = this.mapDTOtoRecordId(dto);
      const recordOrNull = await this.getRecord(recordId);

      return recordOrNull;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[RequestRecordDataUseCase] Error retrieving record data',
        JSON.stringify(error)
      );
      throw error;
    }
  }

  private mapDTOtoRecordId(dto: RequestRecordDataDTO) {
    const recordIdOrError = RecordId.create(dto.recordId);
    if (recordIdOrError.isFailure) throw new Error('Invalid Record ID');

    return recordIdOrError.getValue();
  }

  private async getRecord(id: RecordId) {
    const record = await this.repository.get(id);

    return record;
  }

  private async processRequest(recordId: string, userEmail?: string) {
    try {
      const resultOrError = await this.eventService.publishEvent({
        body: JSON.stringify({ recordId, userEmail }),
        messageGroupId: 'single-record-processing-group',
      });
      if (resultOrError.isFailure) {
        throw new Error(
          `Error publishing event to SQS: ${resultOrError.getErrorValue()}`
        );
      }

      return resultOrError.getValue();
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(
        '[RequestRecordDataUseCase] Error invoking async processor:',
        JSON.stringify(error)
      );
      throw error;
    }
  }
}
