import AWS from 'aws-sdk';
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../shared/infra/base-handler';
import { LogHandlerEvent } from '../../../../shared/infra/decorators/handler';
import { RequestRecordDataUseCase } from './use-case';
import { RequestRecordDataDTO } from './dto';
import { RecordRepository } from '../../repo/record-repository';
import { EventService } from '../../../../shared/services/aws/publish-sqs-event-service';

class Lambda extends BaseHandler implements LambdaInterface {
  private requestRecordDataUseCase: RequestRecordDataUseCase;

  private init() {
    const eventService = new EventService(
      process.env.SQS_QUEUE_URL || '',
      'request-record-data',
      process.env.AWS_REGION || 'us-east-1'
    );
    const recordRepository = new RecordRepository(
      new AWS.DynamoDB({ region: process.env.REGION }),
      process.env.RECORD_TABLE || ''
    );
    this.requestRecordDataUseCase = new RequestRecordDataUseCase(
      recordRepository,
      eventService
    );
  }

  @LogHandlerEvent
  public async handler(event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      const result = await this.requestRecordDataUseCase.execute(dto);

      return this.ok({
        recordId: dto.recordId,
        ...result,
      });
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: APIGatewayProxyEvent): RequestRecordDataDTO {
    const recordId = event.pathParameters?.recordId;
    const userEmail = event.queryStringParameters?.userEmail;
    if (!recordId) {
      throw new Error('Failed to parse request body: recordId is required');
    }

    return {
      recordId,
      userEmail,
    };
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
