import { SQSEvent, Context } from 'aws-lambda';
import { BaseHandler } from '../../../../../shared/infra/base-handler';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { AWSLambdaService } from '../../../../../shared/services/aws/lambda/aws-lambda-service';

class Lambda extends BaseHandler implements LambdaInterface {
  private lambdaService: AWSLambdaService;

  private init() {
    this.lambdaService = new AWSLambdaService(process.env.AWS_REGION || '');
  }

  @LogHandlerEvent
  public async handler(event: SQSEvent, _context: Context) {
    this.init();
    for (const record of event.Records) {
      const recordId = JSON.parse(record.body);
      try {
        // eslint-disable-next-line no-console
        console.log(`Processing message: ${recordId}`);
        await this.invokeResolverFunction(recordId);
        // eslint-disable-next-line no-console
        console.log(`Message Processed Successfully: ${recordId}`);
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error(`Error processing message ${recordId}:`, error);
      }
    }
  }

  private async invokeResolverFunction(recordId: string) {
    try {
      const sqsEvent = {
        Records: [
          {
            body: JSON.stringify({
              recordId,
              shouldSendEmailNotificationOnError: true,
            }),
          },
        ],
      };
      const response = await this.lambdaService.executeProcessSync(
        process.env.PROCESSOR_FUNCTION_NAME || '',
        sqsEvent
      );
      // eslint-disable-next-line no-console
      console.log(`Resolver invoking successfully: ${recordId}`);
      // eslint-disable-next-line no-console
      console.log(`Response: ${JSON.stringify(response)}`);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error invoking resolver:', error);
      throw error;
    }
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
