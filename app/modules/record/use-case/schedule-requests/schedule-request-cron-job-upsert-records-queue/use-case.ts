import { UseCase } from '../../../../../shared/core/use-case';
import { LogUseCaseDTO } from '../../../../../shared/infra/decorators/use-case';
import { ZohoRecordService } from '../../../../../shared/services/zoho/crm/record/zoho-record-service';

export class GetAllRecordsIdUseCase
  implements UseCase<undefined, Promise<string[]>>
{
  constructor(private readonly zohoRecordService: ZohoRecordService) {}

  @LogUseCaseDTO
  public async execute(): Promise<string[]> {
    const result = await this.zohoRecordService.getRecordsSortedByUpdateDate({
      order: 'ASC',
      limit: 100,
      isCacheEnabled: false,
    });
    if (result.isFailure) {
      // eslint-disable-next-line no-console
      console.error(
        `[GetAllRecordsIdUseCase] Error listing field values: ${result.getErrorValue()}`
      );
      throw new Error('Error listing field values');
    }

    return result.getValue().map((item) => item.Name);
  }
}
