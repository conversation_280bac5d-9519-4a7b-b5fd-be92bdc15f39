import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { BaseHandler } from '../../../../../shared/infra/base-handler';
import { LogHandlerEvent } from '../../../../../shared/infra/decorators/handler';
import { GetAllRecordsIdUseCase } from './use-case';
import { SendMessageCommand, SQSClient } from '@aws-sdk/client-sqs';
import { ZohoRecordService } from '../../../../../shared/services/zoho/crm/record/zoho-record-service';
import { SecretsManager } from 'aws-sdk';
import { SecretsManagerService } from '../../../../../shared/services/aws/secret-manager-service';
import { ZohoTokenMiddleware } from '../../../../../shared/middlewares/zoho/zoho-token-middleware';

class Lambda extends BaseHandler implements LambdaInterface {
  private getAllRecordsIdUseCase: GetAllRecordsIdUseCase;

  private sqsClient: SQSClient;

  private init() {
    const secretsManagerService = new SecretsManagerService(
      new SecretsManager({ region: process.env.REGION }),
      process.env.ZOHO_SECRET_ID || ''
    );
    const zohoTokenMiddleware = new ZohoTokenMiddleware({
      secretsManagerService,
    });
    const zohoRecordService = new ZohoRecordService({
      tokenMiddleware: zohoTokenMiddleware,
    });
    this.getAllRecordsIdUseCase = new GetAllRecordsIdUseCase(zohoRecordService);
    this.sqsClient = new SQSClient({ region: process.env.AWS_REGION });
  }

  @LogHandlerEvent
  public async handler(_event: APIGatewayProxyEvent, _context: Context) {
    try {
      this.init();
      const recordList = await this.getAllRecordsIdUseCase.execute();
      await this.sendMessagesToSQS(recordList);

      return this.ok({
        recordList,
        message: `CRON Job triggered at ${new Date().toLocaleString('en-US', {
          timeZone: 'America/Costa_Rica',
        })}`,
      });
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private async sendMessagesToSQS(recordList: string[]) {
    for (const recordId of recordList) {
      const command = new SendMessageCommand({
        QueueUrl: process.env.SQS_QUEUE_URL || '',
        MessageBody: JSON.stringify(recordId),
        MessageGroupId: 'record-processing-group',
      });
      try {
        await this.sqsClient.send(command);
        // eslint-disable-next-line no-console
        console.log(`Message Sent: ${recordId}`);
      } catch (err) {
        // eslint-disable-next-line no-console
        console.error(`Error Sending ${recordId}:`, err);
      }
    }
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
