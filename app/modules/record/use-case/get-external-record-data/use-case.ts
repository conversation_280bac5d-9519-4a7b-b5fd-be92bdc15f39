import { UseCase } from '../../../../shared/core/use-case';
import { ValidationError } from '../../../../shared/domain/errors/domain/validation-error';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../../../shared/services/error-handler';
import { RecordExternalStorageService } from '../../../../shared/services/zoho/crm/record/record-external-storage-service';
import { Record } from '../../domain/Record';
import { RecordId } from '../../domain/record-id';
import { RecordMap } from '../../mappers/record-map';
import { JudiciaryRecordExternalUpdater } from '../../services/judiciary-record-external-updater';
import { RecordAssembler } from '../../services/record-assembler';
import { UpdateRecordUseCase } from '../update-record/use-case';
import { GetExternalRecordDataDTO } from './dto';

export class GetExternalRecordDataUseCase
  implements UseCase<GetExternalRecordDataDTO, Promise<Record>>
{
  private recordId: string;

  private judiciaryRecordExternalUpdater: JudiciaryRecordExternalUpdater;

  private shouldSendEmailOnError: boolean;

  constructor(
    private recordAssembler: RecordAssembler,
    private recordExternalStorageService: RecordExternalStorageService,
    private updateRecordUseCase: UpdateRecordUseCase,
    private errorHandler: ErrorHandler
  ) {
    this.judiciaryRecordExternalUpdater = new JudiciaryRecordExternalUpdater(
      this.recordExternalStorageService
    );
  }

  @LogUseCaseDTO
  public async execute(dto: GetExternalRecordDataDTO): Promise<Record> {
    this.recordId = this.mapDTOtoRecordId(dto);
    this.shouldSendEmailOnError = true;
    // this.shouldSendEmailOnError = !dto.shouldSendEmailNotificationOnError;
    const record = await this.getRecordDataFromJudiciary(dto.userEmail);
    await this.updateLocalRecord(record);
    await this.judiciaryRecordExternalUpdater.updateRecord(record);

    return record;
  }

  private mapDTOtoRecordId(dto: GetExternalRecordDataDTO) {
    try {
      const recordIdOrError = RecordId.create(dto.recordId);
      if (recordIdOrError.isFailure) {
        throw new ValidationError(
          `Invalid record ID format: ${dto.recordId}`,
          this.recordId,
          'INVALID_RECORD_ID',
          {}
        );
      }

      return recordIdOrError.getValue().value;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[RequestRecordDataUseCase] Error mapping DTO to Record ID:',
        JSON.stringify(error)
      );
      throw new ValidationError(
        `Failed to map DTO to Record ID: ${
          error instanceof Error ? error.message : 'An unknown error occurred'
        }`,
        this.recordId,
        'INVALID_RECORD_ID',
        { originalError: error }
      );
    }
  }

  private async getRecordDataFromJudiciary(userEmail?: string) {
    const context = {
      operation: 'GetRecordDataFromJudiciary',
      startTime: Date.now(),
    };
    try {
      return await this.getRecordData();
    } catch (error) {
      if (this.shouldSendEmailOnError)
        await this.errorHandler.handleError(error, this.recordId, {
          context: {
            ...context,
            userEmail,
            duration: Date.now() - context.startTime,
          },
        });
      throw error;
    }
  }

  private async getRecordData(): Promise<Record> {
    const record = await this.recordAssembler.assembleRecordAggregate({
      recordId: this.recordId,
    });

    return record;
  }

  private async updateLocalRecord(record: Record) {
    const dto = RecordMap.toDTO(record);
    await this.updateRecordUseCase.execute({
      record: dto,
    });
  }
}
