import AWS from 'aws-sdk';
import { Context, SQSEvent } from 'aws-lambda';
import { LambdaInterface } from '@aws-lambda-powertools/commons';
import { SecretsManager } from 'aws-sdk';
import { BaseHandler } from '../../../../shared/infra/base-handler';
import { LogHandlerEvent } from '../../../../shared/infra/decorators/handler';
import { SecretsManagerService } from '../../../../shared/services/aws/secret-manager-service';
import { ZohoTokenMiddleware } from '../../../../shared/middlewares/zoho/zoho-token-middleware';
import { ZohoRecordService } from '../../../../shared/services/zoho/crm/record/zoho-record-service';
import { JudiciaryRecordService } from '../../../../shared/services/judiciary/judiciary-record-service';
import { ZohoCredentialService } from '../../../../shared/services/zoho/crm/credentials/zoho-credential-service';
import { RequestRecordAudienceUseCase } from '../../../audience/use-cases/request-record-audience/use-case';
import { RequestRecordAuthorizationsUseCase } from '../../../authorization/use-cases/request-record-authorization/use-case';
import { RequestRecordBasicInfoUseCase } from '../../../basic-info/use-cases/request-record-basic-info/use-case';
import { RequestRecordDocumentsUseCase } from '../../../document/use-cases/request-record-documents/use-case';
import { RequestRecordNotificationsUseCase } from '../../../notification/use-cases/request-record-notifications/use-case';
import { RequestRecordPartsUseCase } from '../../../part/use-cases/request-record-parts/use-case';
import { JudiciaryRecordAssembler } from '../../services/judiciary-record-assembler';
import { ErrorHandler } from '../../../../shared/services/error-handler';
import { ErrorNotificationUseCase } from '../error-notification/use-case';
import { SESEmailService } from '../../../../shared/services/aws/ses-service';
import { AuthenticationDataRepository } from '../../../authentication-data/repo/authentication-data-repository';
import { AuthenticationDataMiddleware } from '../../../authentication-data/middleware/authentication-data-middleware';
import { PuppeteerScraperService } from '../../../../shared/services/judiciary/puppeteer-scrapper-service';
import { GetExternalRecordDataUseCase } from './use-case';
import { GetExternalRecordDataDTO } from './dto';
import { RecordRepository } from '../../repo/record-repository';
import { UpdateRecordUseCase } from '../update-record/use-case';
import { RequestRecordRetentionsUseCase } from '../../../retention/use-cases/request-record-retention/use-case';
import { RequestRecordResolutionsUseCase } from '../../../resolution/use-cases/request-record-resolution/use-case';
import { ZohoWorkDriveService } from '../../../../shared/services/zoho/work-drive/zoho-work-drive-service';
import { GetRecordDataByIdUseCase } from '../get-record-data-by-id/use-case';
import { RequestRecordDossiersUseCase } from '../../../dossier/use-case/request-record-dossier/use-case';

class Lambda extends BaseHandler implements LambdaInterface {
  private getExternalRecordDataUseCase: GetExternalRecordDataUseCase;

  private zohoRecordService: ZohoRecordService;

  private zohoCredentialService: ZohoCredentialService;

  private zohoWorkDriveService: ZohoWorkDriveService;

  private judiciaryRecordService: JudiciaryRecordService;

  private judiciaryRecordAssembler: JudiciaryRecordAssembler;

  private errorHandler: ErrorHandler;

  private initZohoDependencies() {
    const secretsManagerService = new SecretsManagerService(
      new SecretsManager({ region: process.env.REGION }),
      process.env.ZOHO_SECRET_ID || ''
    );
    const zohoTokenMiddleware = new ZohoTokenMiddleware({
      secretsManagerService,
    });
    this.zohoRecordService = new ZohoRecordService({
      tokenMiddleware: zohoTokenMiddleware,
    });
    this.zohoCredentialService = new ZohoCredentialService({
      tokenMiddleware: zohoTokenMiddleware,
    });
    // eslint-disable-next-line no-console
    this.zohoWorkDriveService = new ZohoWorkDriveService({
      parentId: process.env.ZOHO_WORK_DRIVE_FOLDER_PARENT_ID || '',
      tokenMiddleware: zohoTokenMiddleware,
    });
  }

  private initJudiciaryDependencies() {
    const authenticationDataRepository = new AuthenticationDataRepository(
      new AWS.DynamoDB({ region: process.env.REGION }),
      process.env.JUDICIARY_AUTH_DATA_TABLE || ''
    );
    const scraperService = new PuppeteerScraperService();
    const authenticationDataMiddleware = new AuthenticationDataMiddleware(
      authenticationDataRepository,
      scraperService,
      this.zohoCredentialService,
      this.zohoRecordService
    );
    this.judiciaryRecordService = new JudiciaryRecordService({
      middleware: authenticationDataMiddleware,
    });
  }

  private initAggregatorDependencies(recordRepository: RecordRepository) {
    const getRecordDataByIdUseCase = new GetRecordDataByIdUseCase(
      recordRepository
    );
    const requestRecordBasicInfoUseCase = new RequestRecordBasicInfoUseCase(
      this.judiciaryRecordService
    );
    const requestRecordPartsUseCase = new RequestRecordPartsUseCase(
      this.judiciaryRecordService
    );
    const requestRecordNotificationsUseCase =
      new RequestRecordNotificationsUseCase(this.judiciaryRecordService);
    const requestRecordAuthorizationsUseCase =
      new RequestRecordAuthorizationsUseCase(this.judiciaryRecordService);
    const requestRecordRetentionsUseCase = new RequestRecordRetentionsUseCase(
      this.judiciaryRecordService
    );
    const requestRecordResolutionsUseCase = new RequestRecordResolutionsUseCase(
      this.judiciaryRecordService
    );
    const requestRecordAudienceUseCase = new RequestRecordAudienceUseCase(
      this.judiciaryRecordService
    );
    const requestRecordDocumentsUseCase = new RequestRecordDocumentsUseCase(
      this.judiciaryRecordService,
      getRecordDataByIdUseCase,
      this.zohoWorkDriveService
    );
    const requestRecordDossiersUseCase = new RequestRecordDossiersUseCase(
      this.judiciaryRecordService,
      getRecordDataByIdUseCase,
      this.zohoWorkDriveService
    );
    this.judiciaryRecordAssembler = new JudiciaryRecordAssembler(
      requestRecordBasicInfoUseCase,
      requestRecordPartsUseCase,
      requestRecordNotificationsUseCase,
      requestRecordAuthorizationsUseCase,
      requestRecordRetentionsUseCase,
      requestRecordResolutionsUseCase,
      requestRecordAudienceUseCase,
      requestRecordDocumentsUseCase,
      requestRecordDossiersUseCase
    );
  }

  private initErrorHandler() {
    const emailService = new SESEmailService(
      new AWS.SES({ region: process.env.REGION }),
      // TODO: change source email
      '<EMAIL>'
    );
    const errorNotificationUseCase = new ErrorNotificationUseCase(
      emailService,
      this.zohoRecordService
    );
    this.errorHandler = new ErrorHandler(errorNotificationUseCase);
  }

  private init() {
    this.initZohoDependencies();
    this.initJudiciaryDependencies();
    const recordRepository = new RecordRepository(
      new AWS.DynamoDB({ region: process.env.REGION }),
      process.env.RECORD_TABLE || ''
    );
    this.initAggregatorDependencies(recordRepository);
    this.initErrorHandler();
    const updateRecordUseCase = new UpdateRecordUseCase(recordRepository);
    this.getExternalRecordDataUseCase = new GetExternalRecordDataUseCase(
      this.judiciaryRecordAssembler,
      this.zohoRecordService,
      updateRecordUseCase,
      this.errorHandler
    );
  }

  @LogHandlerEvent
  public async handler(event: SQSEvent, _context: Context) {
    try {
      this.init();
      const dto = this.mapEventToDTO(event);
      for (const record of dto) {
        await this.getExternalRecordDataUseCase.execute(record);
      }

      return this.ok(event);
    } catch (error) {
      return this.fail(error as Error);
    }
  }

  private mapEventToDTO(event: SQSEvent): GetExternalRecordDataDTO[] {
    return event.Records.map((record) => JSON.parse(record.body));
  }
}
const handlerClass = new Lambda();
export const handler = handlerClass.handler.bind(handlerClass);
