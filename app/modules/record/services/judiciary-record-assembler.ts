/* eslint-disable no-console */
import { DateValueObject } from '../../../shared/common-value-objects/date-value-object';
import { DomainError } from '../../../shared/domain/errors/domain/domain-error';
import { GenericDomainError } from '../../../shared/domain/errors/domain/generic-domain-error';
import { RequestRecordAudienceUseCase } from '../../audience/use-cases/request-record-audience/use-case';
import { Authorization } from '../../authorization/domain/Authorization';
import { RequestRecordAuthorizationsUseCase } from '../../authorization/use-cases/request-record-authorization/use-case';
import { BasicInfo } from '../../basic-info/domain/BasicInfo';
import { RequestRecordBasicInfoUseCase } from '../../basic-info/use-cases/request-record-basic-info/use-case';
import { Document } from '../../document/domain/Document';
import { RequestRecordDocumentsUseCase } from '../../document/use-cases/request-record-documents/use-case';
import { Dossier } from '../../dossier/domain/Dossier';
import { RequestRecordDossiersUseCase } from '../../dossier/use-case/request-record-dossier/use-case';
import { Notification } from '../../notification/domain/Notification';
import { RequestRecordNotificationsUseCase } from '../../notification/use-cases/request-record-notifications/use-case';
import { Part } from '../../part/domain/Part';
import { RequestRecordPartsUseCase } from '../../part/use-cases/request-record-parts/use-case';
import { Resolution } from '../../resolution/domain/Resolution';
import { RequestRecordResolutionsUseCase } from '../../resolution/use-cases/request-record-resolution/use-case';
import { Retention } from '../../retention/domain/Retention';
import { RequestRecordRetentionsUseCase } from '../../retention/use-cases/request-record-retention/use-case';
import { Record } from '../domain/Record';
import { RecordId } from '../domain/record-id';
import { RecordAssembler } from './record-assembler';

export class JudiciaryRecordAssembler implements RecordAssembler {
  private recordId: string;

  private context: {
    operation: string;
    startTime: number;
  };

  constructor(
    private requestRecordBasicInfoUseCase: RequestRecordBasicInfoUseCase,
    private requestRecordPartsUseCase: RequestRecordPartsUseCase,
    private requestRecordNotificationsUseCase: RequestRecordNotificationsUseCase,
    private requestRecordAuthorizationsUseCase: RequestRecordAuthorizationsUseCase,
    private requestRecordRetentionsUseCase: RequestRecordRetentionsUseCase,
    private requestRecordResolutionsUseCase: RequestRecordResolutionsUseCase,
    private requestRecordAudienceUseCase: RequestRecordAudienceUseCase,
    private requestRecordDocumentsUseCase: RequestRecordDocumentsUseCase,
    private requestRecordDossiersUseCase: RequestRecordDossiersUseCase
  ) {}

  public async assembleRecordAggregate(dto: {
    recordId: string;
  }): Promise<Record> {
    this.recordId = dto.recordId;
    this.context = {
      operation: 'assembleRecordAggregate',
      startTime: Date.now(),
    };
    // eslint-disable-next-line no-console
    console.log(
      '[JudiciaryRecordAssembler] Starting data aggregation',
      this.context
    );
    try {
      const {
        basicInfo,
        parts,
        notifications,
        authorization,
        retentions,
        resolutions,
        documents,
        dossiers,
      } = await this.executeLinearRequests(dto.recordId);
      const recordLinear = this.mapLinearRequestsResultToRecord(
        dto.recordId,
        basicInfo,
        parts,
        notifications,
        authorization,
        retentions,
        resolutions,
        documents,
        dossiers
      );

      return recordLinear;
    } catch (error) {
      const executionTime = Date.now() - this.context.startTime;
      // eslint-disable-next-line no-console
      console.error(
        '[JudiciaryRecordAssembler] Encountered an error while aggregating data',
        {
          ...this.context,
          executionTime,
          error: JSON.stringify(error),
        }
      );
      if (error instanceof DomainError) {
        throw error;
      }
      throw new GenericDomainError(
        'Unexpected error occurred while fetching record data from judiciary',
        this.recordId,
        error as Error,
        {
          ...this.context,
          error: JSON.stringify(error),
        }
      );
    }
  }

  private mapDTOToRecordId(recordId: string): string {
    const RecordIdOrError = RecordId.create(recordId);
    if (RecordIdOrError.isFailure) {
      throw new Error('Invalid RecordId');
    }

    return RecordIdOrError.getValue().value;
  }

  private async executeLinearRequests(recordId: string) {
    console.log(
      `[JudiciaryRecordAssembler][ExecuteLinearRequests] Starting Assembler...`
    );
    console.log(
      `[JudiciaryRecordAssembler][ExecuteLinearRequests] Requesting Basic Info...`
    );
    const basicInfoOrError = await this.requestRecordBasicInfoUseCase.execute({
      recordId,
    });
    if (basicInfoOrError instanceof DomainError) {
      throw basicInfoOrError;
    }
    const { context, folder } = basicInfoOrError;
    console.log(
      `[JudiciaryRecordAssembler][ExecuteLinearRequests] Requesting Parts...`
    );
    const partsOrError = await this.requestRecordPartsUseCase.execute({
      recordId,
      context,
      folder,
    });
    if (partsOrError instanceof DomainError) {
      throw partsOrError;
    }
    console.log(
      `[JudiciaryRecordAssembler][ExecuteLinearRequests] Requesting Notifications...`
    );
    const notificationsOrError =
      await this.requestRecordNotificationsUseCase.execute({
        recordId,
        context,
        folder,
      });
    if (notificationsOrError instanceof DomainError) {
      throw notificationsOrError;
    }
    console.log(
      `[JudiciaryRecordAssembler][ExecuteLinearRequests] Requesting Authorizations...`
    );
    const authorizationOrError =
      await this.requestRecordAuthorizationsUseCase.execute({
        recordId,
        context,
        folder,
      });
    if (authorizationOrError instanceof DomainError) {
      throw authorizationOrError;
    }
    console.log(
      `[JudiciaryRecordAssembler][ExecuteLinearRequests] Requesting Retentions...`
    );
    const retentionsOrError = await this.requestRecordRetentionsUseCase.execute(
      {
        recordId,
        context,
        folder,
      }
    );
    if (retentionsOrError instanceof DomainError) {
      throw retentionsOrError;
    }
    console.log(
      `[JudiciaryRecordAssembler][ExecuteLinearRequests] Requesting Resolutions...`
    );
    const resolutionsOrError =
      await this.requestRecordResolutionsUseCase.execute({
        recordId,
        context,
        folder,
      });
    if (resolutionsOrError instanceof DomainError) {
      throw resolutionsOrError;
    }
    console.log(
      `[JudiciaryRecordAssembler][ExecuteLinearRequests] Requesting Documents...`
    );
    const documentsOrError = await this.requestRecordDocumentsUseCase.execute({
      recordId,
      context,
      folder,
    });
    if (documentsOrError instanceof DomainError) {
      throw documentsOrError;
    }
    console.log(
      `[JudiciaryRecordAssembler][ExecuteLinearRequests] Requesting Dossiers...`
    );
    const dossiersOrError = await this.requestRecordDossiersUseCase.execute({
      recordId,
      context,
      folder,
    });
    if (dossiersOrError instanceof DomainError) {
      throw dossiersOrError;
    }
    console.log(
      `[JudiciaryRecordAssembler][ExecuteLinearRequests] Assembler finished!`
    );

    return {
      basicInfo: basicInfoOrError,
      parts: partsOrError,
      notifications: notificationsOrError,
      authorization: authorizationOrError,
      retentions: retentionsOrError,
      resolutions: resolutionsOrError,
      documents: documentsOrError,
      dossiers: dossiersOrError,
    };
  }

  private mapLinearRequestsResultToRecord(
    dto: string,
    basicInfo: BasicInfo,
    parts: Part[],
    notifications: Notification[],
    authorizations: Authorization[],
    retentions: Retention[],
    resolutions: Resolution[],
    documents: Document[],
    dossiers: Dossier[]
  ) {
    const recordId = this.mapDTOToRecordId(dto);
    const recordProps = {
      recordId: RecordId.create(recordId).getValue(),
      basicInfo,
      parts,
      notifications,
      authorizations,
      retentions,
      resolutions,
      documents,
      dossiers,
      createdAt: DateValueObject.create('createdAt').getValue(),
      updatedAt: DateValueObject.create('updatedAt').getValue(),
    };
    const record = Record.create(recordProps as Record);

    return record.getValue();
  }
}
