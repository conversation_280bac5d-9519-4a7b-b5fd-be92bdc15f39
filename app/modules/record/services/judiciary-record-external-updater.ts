import { Result } from '../../../shared/core/Result';
import { ExternalServiceError } from '../../../shared/domain/errors/domain/external-service-error';
import { RecordExternalStorageService } from '../../../shared/services/zoho/crm/record/record-external-storage-service';
import { BasicInfoMap } from '../../basic-info/mappers/basic-info-map';
import { Part } from '../../part/domain/Part';
import { ExternalPartDTO } from '../../part/dto/external-part-dto';
import { PartMap } from '../../part/mappers/part-map';
import { Record } from '../domain/Record';
import { DomainError } from '../../../shared/domain/errors/domain/domain-error';
import { ErrorFactory } from '../../../shared/domain/errors/domain/error-factory';
import { NotificationMap } from '../../notification/mappers/notification-map';
import { Notification } from '../../notification/domain/Notification';
import { ExternalNotificationDTO } from '../../notification/dto/external-notification-dto';
import { Authorization } from '../../authorization/domain/Authorization';
import { AuthorizationMap } from '../../authorization/mappers/authorization-map';
import { ExternalAuthorizationDTO } from '../../authorization/dto/external-authorization-dto';
import { RetentionMap } from '../../retention/mappers/retention-map';
import { Retention } from '../../retention/domain/Retention';
import { ExternalRetentionDTO } from '../../retention/dto/external-retention-dto';
import { ResolutionMap } from '../../resolution/mappers/resolution-map';
import { Resolution } from '../../resolution/domain/Resolution';
import { ExternalResolutionDTO } from '../../resolution/dto/external-resolution-dto';
import { Document } from '../../document/domain/Document';
import { DocumentMap } from '../../document/mappers/document-map';
import { ExternalDocumentDTO } from '../../document/dto/external-document-dto';
import { DossierMap } from '../../dossier/mappers/dossier-map';
import { Dossier } from '../../dossier/domain/Dossier';
import { DossierDocument } from '../../dossier/domain/DossierDocument';
import { DossierDocumentMap } from '../../dossier/mappers/dossier-document-map';
import {
  ExternalDossierDocumentDTO,
  ExternalRecordDossierDTO,
} from '../../dossier/dto/external-dossier-dto';

export class JudiciaryRecordExternalUpdater {
  private externalRecordId: string;

  private recordId: string;

  constructor(
    private recordExternalStorageService: RecordExternalStorageService
  ) {}

  private async init(): Promise<void> {
    try {
      const externalRecordOrError =
        await this.recordExternalStorageService.searchForExternalId({
          module: 'Cobros_Judiciales',
          queryParam: 'Name',
          queryValue: this.recordId,
        });
      if (externalRecordOrError.isFailure) {
        throw externalRecordOrError.getErrorValue();
      }
      this.externalRecordId = externalRecordOrError.getValue().data[0].id;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[JudiciaryRecordUpdater] Error initializing external record:',
        error
      );
      throw error;
    }
  }

  public async updateRecord(record: Record): Promise<void> {
    this.recordId = record.recordId.value;
    try {
      await this.init();
      this.updateBasicInfo(record),
        await Promise.all([
          this.updateParts(record),
          this.updateNotifications(record),
          this.updateAuthorizations(record),
          this.updateRetentions(record),
          this.updateResolutions(record),
          this.updateDocuments(record),
          this.updateDossier(record),
        ]);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[JudiciaryRecordUpdater] Encounter an error during the update of the external record:',
        error
      );
      if (error instanceof DomainError) {
        throw error;
      }
      throw ErrorFactory.createFromError(error, this.recordId, {
        originalError: error,
        date: new Date().toISOString(),
      });
    }
  }

  private async updateBasicInfo(record: Record): Promise<void> {
    const { recordId, basicInfo } = record;
    // eslint-disable-next-line no-console
    console.log('[JudiciaryRecordUpdater] Updating Record Basic Info...');
    const dto = BasicInfoMap.toExternalPersistence(recordId.value, basicInfo);
    try {
      await this.recordExternalStorageService.update({
        module: 'Cobros_Judiciales',
        externalId: this.externalRecordId,
        data: dto,
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[JudiciaryRecordUpdater] Error updating record basic info:',
        error
      );
      throw new ExternalServiceError(
        `[JudiciaryRecordUpdater] Error updating basic info: ${
          error instanceof Error ? error.message : 'An unknown error occurred'
        }`,
        this.recordId,
        'JudiciaryRecordUpdater => updateBasicInfo',
        {
          module: 'Cobros_Judiciales',
          externalId: this.externalRecordId,
          data: dto,
          date: new Date().toISOString(),
        }
      );
    }
  }

  private async updateParts(record: Record): Promise<void> {
    const { parts: newParts } = record;
    // eslint-disable-next-line no-console
    console.log(
      '[JudiciaryRecordUpdater] Updating Record Parts...',
      JSON.stringify(newParts)
    );
    try {
      const currentParts = await this.getCurrentExternalRelatedList<Part>(
        {
          parentModule: 'Cobros_Judiciales',
          module: 'Partes',
          externalId: this.externalRecordId,
          fields: 'Name,Identificaci_n,Tipo_de_Intervenci_n',
        },
        PartMap.fromExternalStorageToDomain
      ); // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Parts from external storage:',
        currentParts.map(PartMap.toDTO)
      );
      const { createList, updateList } = this.getUpsertList<Part>(
        {
          currentList: currentParts,
          newList: newParts,
        },
        (part) => {
          return `${part.documentId.value}-${part.type}`;
        },
        (currentPart, newPart) => {
          return (
            currentPart.name !== newPart.name ||
            currentPart.documentType !== newPart.documentType ||
            currentPart.type !== newPart.type
          );
        },
        (currentPart, newPart) => {
          newPart.externalId = currentPart.externalId;

          return newPart;
        }
      );
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Parts to create:',
        createList.map(PartMap.toDTO)
      );
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Parts to update:',
        updateList.map(PartMap.toDTO)
      );
      await this.upsertElements<Part, ExternalPartDTO>(
        {
          externalRecordId: this.externalRecordId,
          createList,
          updateList,
        },
        'Partes',
        (part) => {
          return part.externalId?.value || '';
        },
        PartMap.toExternalStorage
      );
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[JudiciaryRecordUpdater] Error updating record parts:',
        error
      );
      throw new ExternalServiceError(
        `[JudiciaryRecordUpdater] Error updating record parts: ${
          error instanceof Error ? error.message : 'An unknown error occurred'
        }`,
        this.recordId,
        'JudiciaryRecordUpdater => updateParts',
        {
          externalId: this.externalRecordId,
          data: { parts: newParts },
          date: new Date().toISOString(),
        }
      );
    }
  }

  private async updateNotifications(record: Record): Promise<void> {
    const { notifications: newNotifications } = record;
    // eslint-disable-next-line no-console
    console.log(
      '[JudiciaryRecordUpdater] Updating Record Notification...',
      JSON.stringify(newNotifications)
    );
    try {
      const currentNotification =
        await this.getCurrentExternalRelatedList<Notification>(
          {
            parentModule: 'Cobros_Judiciales',
            module: 'Notificaciones_del_Expediente',
            externalId: this.externalRecordId,
            fields:
              'Name,Fecha_comunicaci_n_de_la_notificaci_n,Fecha_resoluci_n_de_la_notificaci_n,Estado_de_la_notificaci_n,Parte_del_Procesos,Representante,Medio_de_notificaci_n',
          },
          NotificationMap.fromExternalStorageToDomain
        ); // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Notifications from external storage:',
        currentNotification.map(NotificationMap.toDTO)
      );
      const { createList, updateList } = this.getUpsertList<Notification>(
        {
          currentList: currentNotification,
          newList: newNotifications,
        },
        (notification) => {
          return notification.name.value.toString();
        },
        (currentNotification, newNotification) => {
          return (
            currentNotification.resolutionDate !==
              newNotification.resolutionDate ||
            currentNotification.communicationDate !==
              newNotification.communicationDate ||
            currentNotification.status !== newNotification.status ||
            currentNotification.part !== newNotification.part
          );
        },
        (currentNotification, newNotification) => {
          newNotification.externalId = currentNotification.externalId;

          return newNotification;
        }
      );
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Notifications to create:',
        createList.map(NotificationMap.toDTO)
      );
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Notifications to update:',
        updateList.map(NotificationMap.toDTO)
      );
      await this.upsertElements<Notification, ExternalNotificationDTO>(
        {
          externalRecordId: this.externalRecordId,
          createList,
          updateList,
        },
        'Notificaciones',
        (part) => {
          return part.externalId?.value || '';
        },
        NotificationMap.toExternalStorage
      );
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[JudiciaryRecordUpdater] Error updating record notifications:',
        error
      );
      throw new ExternalServiceError(
        `[JudiciaryRecordUpdater] Error updating record notifications: ${
          error instanceof Error ? error.message : 'An unknown error occurred'
        }`,
        this.recordId,
        'JudiciaryRecordUpdater => updateNotifications',
        {
          externalId: this.externalRecordId,
          data: { parts: newNotifications },
          date: new Date().toISOString(),
        }
      );
    }
  }

  private async updateAuthorizations(record: Record): Promise<void> {
    const { authorizations: newAuthorizations } = record;
    // eslint-disable-next-line no-console
    console.log(
      '[JudiciaryRecordUpdater] Updating Record Authorization...',
      JSON.stringify(newAuthorizations)
    );
    try {
      const currentAuthorization =
        await this.getCurrentExternalRelatedList<Authorization>(
          {
            parentModule: 'Cobros_Judiciales',
            module: 'Autorizaciones',
            externalId: this.externalRecordId,
            fields:
              'Autorizado,Currency,Estado,Fecha,Monto_a_retirar,N_mero_autorizaci_n,Name,No_Dep_sito,Obligado,Observaciones',
          },
          AuthorizationMap.fromExternalStorageToDomain
        ); // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Authorizations from external storage:',
        currentAuthorization.map(AuthorizationMap.toDTO)
      );
      const { createList, updateList } = this.getUpsertList<Authorization>(
        {
          currentList: currentAuthorization,
          newList: newAuthorizations,
        },
        (authorization) => {
          return authorization.number.value.toString();
        },
        (currentAuthorization, newAuthorization) => {
          return (
            currentAuthorization.number !== newAuthorization.number ||
            currentAuthorization.amount !== newAuthorization.amount ||
            currentAuthorization.status !== newAuthorization.status
          );
        },
        (currentAuthorization, newAuthorization) => {
          newAuthorization.externalId = currentAuthorization.externalId;

          return newAuthorization;
        }
      );
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Authorizations to create:',
        createList.map(AuthorizationMap.toDTO)
      );
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Authorizations to update:',
        updateList.map(AuthorizationMap.toDTO)
      );
      await this.upsertElements<Authorization, ExternalAuthorizationDTO>(
        {
          externalRecordId: this.externalRecordId,
          createList,
          updateList,
        },
        'Autorizaciones',
        (part) => {
          return part.externalId?.value || '';
        },
        AuthorizationMap.toExternalStorage
      );
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[JudiciaryRecordUpdater] Error updating record authorizations:',
        error
      );
      throw new ExternalServiceError(
        `[JudiciaryRecordUpdater] Error updating record authorizations: ${
          error instanceof Error ? error.message : 'An unknown error occurred'
        }`,
        this.recordId,
        'JudiciaryRecordUpdater => updateAuthorizations',
        {
          externalId: this.externalRecordId,
          data: { parts: newAuthorizations },
          date: new Date().toISOString(),
        }
      );
    }
  }

  private async updateRetentions(record: Record): Promise<void> {
    const { retentions: newRetentions } = record;
    // eslint-disable-next-line no-console
    console.log(
      '[JudiciaryRecordUpdater] Updating Record Retentions...',
      JSON.stringify(newRetentions)
    );
    try {
      const currentRetention =
        await this.getCurrentExternalRelatedList<Retention>(
          {
            parentModule: 'Cobros_Judiciales',
            module: 'Retenciones',
            externalId: this.externalRecordId,
            fields:
              'Name,Obligado,Monto_del_dep_sito,Currency,Saldo_por_autorizaci_n,Saldo_por_pagar,Tipo,No_de_Dep_sito,Fecha',
          },
          RetentionMap.fromExternalStorageToDomain
        ); // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Retentions from external storage:',
        currentRetention.map(RetentionMap.toDTO)
      );
      const { createList, updateList } = this.getUpsertList<Retention>(
        {
          currentList: currentRetention,
          newList: newRetentions,
        },
        (retention) => {
          return retention.number.value.toString();
        },
        (currentRetention, newRetention) => {
          return (
            currentRetention.number !== newRetention.number ||
            currentRetention.amount !== newRetention.amount ||
            currentRetention.balanceToBeAuthorized !==
              newRetention.balanceToBeAuthorized ||
            currentRetention.balanceToBePay !== newRetention.balanceToBePay
          );
        },
        (currentRetention, newRetention) => {
          newRetention.externalId = currentRetention.externalId;

          return newRetention;
        }
      );
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Retentions to create:',
        createList.map(RetentionMap.toDTO)
      );
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Retentions to update:',
        updateList.map(RetentionMap.toDTO)
      );
      await this.upsertElements<Retention, ExternalRetentionDTO>(
        {
          externalRecordId: this.externalRecordId,
          createList,
          updateList,
        },
        'Retenciones',
        (part) => {
          return part.externalId?.value || '';
        },
        RetentionMap.toExternalStorage
      );
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[JudiciaryRecordUpdater] Error updating record retentions:',
        error
      );
      throw new ExternalServiceError(
        `[JudiciaryRecordUpdater] Error updating record retentions: ${
          error instanceof Error ? error.message : 'An unknown error occurred'
        }`,
        this.recordId,
        'JudiciaryRecordUpdater => updateRetentions',
        {
          externalId: this.externalRecordId,
          data: { parts: newRetentions },
          date: new Date().toISOString(),
        }
      );
    }
  }

  private async updateResolutions(record: Record): Promise<void> {
    const { resolutions: newResolutions } = record;
    // eslint-disable-next-line no-console
    console.log(
      '[JudiciaryRecordUpdater] Updating Record Resolutions...',
      JSON.stringify(newResolutions)
    );
    try {
      const currentResolution =
        await this.getCurrentExternalRelatedList<Resolution>(
          {
            parentModule: 'Cobros_Judiciales',
            module: 'Resoluciones',
            externalId: this.externalRecordId,
            fields:
              'Name,Descripcion,Fecha,Tipo_de_Resoluci_n,Resultado,Numero_de_voto,Fecha_del_voto,Juez_redactor,Por_tanto,Documento_de_Resoluci_n',
          },
          ResolutionMap.fromExternalStorageToDomain
        ); // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Resolutions from external storage:',
        currentResolution.map(ResolutionMap.toDTO)
      );
      const { createList, updateList } = this.getUpsertList<Resolution>(
        {
          currentList: currentResolution,
          newList: newResolutions,
        },
        (resolution) => {
          return resolution.name.value.toString();
        },
        (currentResolution, newResolution) => {
          return (
            currentResolution.result !== newResolution.result ||
            currentResolution.therefore !== newResolution.therefore
          );
        },
        (currentResolution, newResolution) => {
          newResolution.externalId = currentResolution.externalId;

          return newResolution;
        }
      );
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Resolutions to create:',
        createList.map(ResolutionMap.toDTO)
      );
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Resolutions to update:',
        updateList.map(ResolutionMap.toDTO)
      );
      await this.upsertElements<Resolution, ExternalResolutionDTO>(
        {
          externalRecordId: this.externalRecordId,
          createList,
          updateList,
        },
        'Resoluciones',
        (part) => {
          return part.externalId?.value || '';
        },
        ResolutionMap.toExternalStorage
      );
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[JudiciaryRecordUpdater] Error updating record resolutions:',
        error
      );
      throw new ExternalServiceError(
        `[JudiciaryRecordUpdater] Error updating record resolutions: ${
          error instanceof Error ? error.message : 'An unknown error occurred'
        }`,
        this.recordId,
        'JudiciaryRecordUpdater => updateResolutions',
        {
          externalId: this.externalRecordId,
          data: { parts: newResolutions },
          date: new Date().toISOString(),
        }
      );
    }
  }

  private async updateDocuments(record: Record): Promise<void> {
    const { documents: newDocuments } = record;
    // eslint-disable-next-line no-console
    console.log(
      '[JudiciaryRecordUpdater] Updating Record Documents...',
      JSON.stringify(newDocuments)
    );
    try {
      const currentDocuments =
        await this.getCurrentExternalRelatedList<Document>(
          {
            parentModule: 'Cobros_Judiciales',
            module: 'Documentos_del_Expediente',
            externalId: this.externalRecordId,
            fields:
              'Descripci_n,Fecha,Estado,Tipo_de_Documento,Descargar_Documento,Identificador,Expediente',
          },
          DocumentMap.fromExternalStorageToDomain
        );
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Documents from external storage:',
        currentDocuments.map(DocumentMap.toDTO)
      );
      const { createList, updateList } = this.getUpsertList<Document>(
        {
          currentList: currentDocuments,
          newList: newDocuments,
        },
        (document) => {
          return document.id.value.toString();
        },
        (currentDocument, newDocument) => {
          return (
            currentDocument.pdf.value !== newDocument.pdf.value ||
            currentDocument.status.value !== newDocument.status.value
          );
        },
        (currentDocument, newDocument) => {
          newDocument.externalId = currentDocument.externalId;

          return newDocument;
        }
      );
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Documents to create:',
        createList.map(DocumentMap.toDTO)
      );
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Documents to update:',
        updateList.map(DocumentMap.toDTO)
      );
      await this.upsertElements<Document, ExternalDocumentDTO>(
        {
          externalRecordId: this.externalRecordId,
          createList,
          updateList,
        },
        'Documentos_del_Expediente',
        (part) => {
          return part.externalId?.value || '';
        },
        DocumentMap.toExternalStorage
      );
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[JudiciaryRecordUpdater] Error updating record documents:',
        error
      );
      throw new ExternalServiceError(
        `[JudiciaryRecordUpdater] Error updating record documents: ${
          error instanceof Error ? error.message : 'An unknown error occurred'
        }`,
        this.recordId,
        'JudiciaryRecordUpdater => updateDocuments',
        {
          externalId: this.externalRecordId,
          data: { parts: newDocuments },
          date: new Date().toISOString(),
        }
      );
    }
  }

  private async updateDossier(record: Record): Promise<void> {
    const { dossiers } = record;
    // eslint-disable-next-line no-console
    console.log('[JudiciaryRecordUpdater] Updating Dossier...');
    try {
      const currentDossierList =
        await this.getCurrentExternalRelatedList<Dossier>(
          {
            parentModule: 'Cobros_Judiciales',
            module: 'Expediente_Asociado1',
            externalId: this.externalRecordId,
            fields:
              'Estado,Expediente_Asociado,Fecha_estado,Fecha_entrada,D_gito_verificador,Descripci_n,Clase,Oficina_Judicial,Direcci_n,Juez_Tramitador,Juez_Decisor,N_mero_de_expediente',
          },
          DossierMap.fromExternalStorageToDomain
        );
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Dossier from external storage:',
        JSON.stringify(currentDossierList)
      );
      const documents = dossiers.map((dossier) => dossier.documents).flat();
      // eslint-disable-next-line no-console
      console.log('[JudiciaryRecordUpdater] Getting Dossier Documents...');
      const currentDossierDocuments =
        await this.getCurrentExternalRelatedList<DossierDocument>(
          {
            parentModule: 'Cobros_Judiciales',
            module: 'Documentos_del_Legajo',
            externalId: this.externalRecordId,
            fields:
              'Descripci_n,Fecha,Estado,Tipo_de_documento,Link_al_documento,Identificador,Expediente_Asociado,Legajo_Asociado,id',
          },
          DossierDocumentMap.fromExternalStorageToDomain
        );
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Dossier Documents from external storage:',
        JSON.stringify(currentDossierDocuments)
      );
      const { createList, updateList } = this.getUpsertList<Dossier>(
        {
          currentList: currentDossierList,
          newList: dossiers,
        },
        (dossier) => {
          return dossier.dossierId.value.toString();
        },
        (currentDossier, newDossier) => {
          return (
            currentDossier.dossierId.value !== newDossier.dossierId.value ||
            currentDossier.status.value !== newDossier.status.value ||
            currentDossier.description.value !== newDossier.description.value ||
            currentDossier.decidingJudge.value !==
              newDossier.decidingJudge.value ||
            currentDossier.processingJudge.value !==
              newDossier.processingJudge.value ||
            currentDossier.class.value !== newDossier.class.value
          );
        },
        (currentDossier, newDossier) => {
          newDossier.externalId = currentDossier.externalId;

          return newDossier;
        }
      );
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Dossier to create:',
        createList.map(DossierMap.toDTO)
      );
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Dossier to update:',
        updateList.map(DossierMap.toDTO)
      );
      await this.upsertElements<Dossier, ExternalRecordDossierDTO>(
        {
          externalRecordId: this.externalRecordId,
          createList,
          updateList,
        },
        'Legajos',
        (part) => {
          return part.externalId?.value || '';
        },
        DossierMap.toExternalStorage
      );
      const externalIdCache = new Map<string, string>();
      for (const doc of documents) {
        const dossierId = doc.dossierId?.value;
        if (!dossierId) continue;
        if (!externalIdCache.has(dossierId)) {
          const externalRecordOrError =
            await this.recordExternalStorageService.searchForExternalId({
              module: 'Legajos',
              queryParam: 'N_mero_de_expediente',
              queryValue: dossierId,
            });
          if (!externalRecordOrError.isFailure) {
            const externalId = externalRecordOrError.getValue().data[0].id;
            externalIdCache.set(dossierId, externalId);
          } else {
            // eslint-disable-next-line no-console
            console.warn(`externalId for dossierId Not Found: ${dossierId}`);
          }
        }
        const externalId = externalIdCache.get(dossierId);
        if (externalId) {
          doc.updateExternalDossierId(externalId);
        }
      }
      // eslint-disable-next-line no-console
      console.log('[JudiciaryRecordUpdater] Updating Dossier Documents...');
      const { createList: createDocumentList, updateList: updateDocumentList } =
        this.getUpsertList<DossierDocument>(
          {
            currentList: currentDossierDocuments,
            newList: documents,
          },
          (dossier) => {
            return dossier.id.value.toString();
          },
          (currentDossier, newDossier) => {
            return (
              currentDossier.status.value !== newDossier.status.value ||
              currentDossier.pdf.value !== newDossier.pdf.value ||
              currentDossier.description.value !==
                newDossier.description.value ||
              currentDossier.type.value !== newDossier.type.value
            );
          },
          (currentDossier, newDossier) => {
            newDossier.externalId = currentDossier.externalId;

            return newDossier;
          }
        );
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Dossier Documents to create:',
        createDocumentList.map(DossierDocumentMap.toDTO)
      );
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Dossier Documents to update:',
        updateDocumentList.map(DossierDocumentMap.toDTO)
      );
      await this.upsertElements<DossierDocument, ExternalDossierDocumentDTO>(
        {
          externalRecordId: this.externalRecordId,
          createList: createDocumentList,
          updateList: updateDocumentList,
        },
        'Documento_del_Legajo',
        (part) => {
          return part.externalId?.value || '';
        },
        DossierDocumentMap.toExternalStorage
      );
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('[JudiciaryRecordUpdater] Error updating dossier:', error);
      throw new ExternalServiceError(
        `[JudiciaryRecordUpdater] Error updating dossier: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        this.recordId,
        'JudiciaryRecordUpdater => updateDossier',
        {
          externalId: this.externalRecordId,
          date: new Date().toISOString(),
        }
      );
    }
  }

  private async getCurrentExternalRelatedList<T>(
    params: {
      parentModule: string;
      module: string;
      externalId: string;
      fields: string;
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    domainMap: (raw: any) => Result<T>
  ): Promise<T[]> {
    try {
      const relatedListOrError =
        await this.recordExternalStorageService.getRelatedList<{
          data: ExternalPartDTO[];
        }>({
          parentModule: params.parentModule,
          module: params.module,
          externalId: params.externalId,
          fields: params.fields,
        });
      if (relatedListOrError.isFailure) {
        throw relatedListOrError.getErrorValue();
      }
      const relatedListResult = relatedListOrError.getValue();
      // eslint-disable-next-line no-console
      console.log(
        '[JudiciaryRecordUpdater] Related list value found on external storage:',
        JSON.stringify(relatedListResult)
      );
      if (!relatedListResult.data || !Array.isArray(relatedListResult.data)) {
        // eslint-disable-next-line no-console
        console.log(
          '[JudiciaryRecordUpdater] No related list found in external storage for module:',
          params.module,
          'returning empty list'
        );

        return [];
      }
      const domainRelatedListOrError = relatedListResult.data.map(domainMap);
      if (domainRelatedListOrError.some((element) => element.isFailure)) {
        throw domainRelatedListOrError
          .filter((element) => element.isFailure)
          .map((element) => element.getErrorValue())
          .join(', ');
      }
      const relatedList = domainRelatedListOrError.map((element) =>
        element.getValue()
      );

      return relatedList;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        `[JudiciaryRecordUpdater] Error getting ${params.module} related list:`,
        error
      );
      throw new ExternalServiceError(
        `[JudiciaryRecordUpdater]  Error getting ${
          params.module
        } related list: ${
          error instanceof Error ? error.message : 'An unknown error occurred'
        }`,
        this.recordId,
        'JudiciaryRecordUpdater => getCurrentExternalRelatedList',
        {
          data: params,
          date: new Date().toISOString(),
        }
      );
    }
  }

  private getUpsertList<T>(
    params: { currentList: T[]; newList: T[] },
    keyGenerator: (e: T) => string,
    updateValidator: (c: T, n: T) => boolean,
    updateCurrentValidator: (c: T, n: T) => T
  ) {
    const currentMap = new Map<string, T>();
    params.currentList.forEach((element) => {
      const key = keyGenerator(element);
      currentMap.set(key, element);
    });
    const upsertList: T[] = [];
    const updateList: T[] = [];
    for (const newElement of params.newList) {
      const key = keyGenerator(newElement);
      const currentPart = currentMap.get(key);
      if (!currentPart) {
        upsertList.push(newElement);
      } else if (updateValidator(currentPart, newElement)) {
        const updatedElement = updateCurrentValidator(currentPart, newElement);
        updateList.push(updatedElement);
      }
    }

    return {
      createList: upsertList,
      updateList,
    };
  }

  // eslint-disable-next-line @typescript-eslint/naming-convention
  private async upsertElements<T, Y extends object>(
    params: { externalRecordId: string; createList: T[]; updateList: T[] },
    module: string,
    getExternalId: (e: T) => string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    externalStorageMap: (recordId: string, raw: T) => Result<Y>
  ) {
    try {
      for (const upsertElement of params.createList) {
        // eslint-disable-next-line no-console
        console.log(
          '[JudiciaryRecordUpdater] Creating element:',
          upsertElement
        );
        const dataOrError = externalStorageMap(
          params.externalRecordId,
          upsertElement
        );
        if (dataOrError.isFailure) {
          throw dataOrError.getErrorValue();
        }
        const data = dataOrError.getValue();
        await this.recordExternalStorageService.create({
          module,
          data,
        });
      }
      for (const upsertElement of params.updateList) {
        // eslint-disable-next-line no-console
        console.log(
          '[JudiciaryRecordUpdater] Updating element:',
          upsertElement
        );
        const dataOrError = externalStorageMap(
          params.externalRecordId,
          upsertElement
        );
        if (dataOrError.isFailure) {
          throw dataOrError.getErrorValue();
        }
        const data = dataOrError.getValue();
        const externalId = getExternalId(upsertElement);
        if (!externalId) {
          // eslint-disable-next-line no-console
          console.error(
            '[JudiciaryRecordUpdater] External ID not found for update:',
            upsertElement
          );

          return;
        }
        await this.recordExternalStorageService.update({
          module,
          externalId,
          data,
        });
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[JudiciaryRecordUpdater] Error upserting elements:',
        error
      );
      throw new ExternalServiceError(
        `[JudiciaryRecordUpdater] Error upserting elements: ${
          error instanceof Error ? error.message : 'An unknown error occurred'
        }`,
        this.recordId,
        'JudiciaryRecordUpdater => upsertElements',
        {
          data: params,
          date: new Date().toISOString(),
        }
      );
    }
  }
}
