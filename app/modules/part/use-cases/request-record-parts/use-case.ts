import { DomainError } from '../../../../shared/domain/errors/domain/domain-error';
import { ExternalServiceError } from '../../../../shared/domain/errors/domain/external-service-error';
import { ValidationError } from '../../../../shared/domain/errors/domain/validation-error';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';
import {
  ExternalRecordService,
  RequestRecordDataArrayResponseDTO,
} from '../../../../shared/services/judiciary/types';
import { RecordId } from '../../../record/domain/record-id';
import { Part } from '../../domain/Part';
import { PartMap } from '../../mappers/part-map';
import { RequestRecordPartsDTO } from './dto';

export class RequestRecordPartsUseCase {
  private recordId: string;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private context: any;

  private folder: string | undefined;

  constructor(private externalRecordService: ExternalRecordService) {}

  @LogUseCaseDTO
  public async execute(
    dto: RequestRecordPartsDTO
  ): Promise<Part[] | DomainError> {
    try {
      this.recordId = this.mapDTOtoRecordId(dto);
      this.context = dto.context;
      this.folder = dto.folder;
      const result = await this.gerRecordDataFromJudiciary();

      return this.mapJudiciaryRecordToDomain(result);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[RequestRecordPartsUseCase] Error executing use case:',
        JSON.stringify(error)
      );
      if (error instanceof DomainError) {
        // eslint-disable-next-line no-console
        console.log('[RequestRecordPartsUseCase] DomainError:', error);

        return error;
      }

      return new ExternalServiceError(
        `[RequestRecordPartsUseCase] Error executing use case: ${
          error instanceof Error ? error.message : 'An unknown error occurred'
        }`,
        dto.recordId,
        'RequestRecordPartsUseCase => execute',
        {
          module: 'Parts',
          originalError: JSON.stringify(error),
          requestData: dto,
        }
      );
    }
  }

  private mapDTOtoRecordId(dto: RequestRecordPartsDTO) {
    try {
      const recordIdOrError = RecordId.create(dto.recordId);
      if (recordIdOrError.isFailure) {
        throw recordIdOrError.getErrorValue();
      }

      return recordIdOrError.getValue().value;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[RequestRecordPartsUseCase] Error mapping DTO to Record ID:',
        JSON.stringify(error)
      );
      throw new ValidationError(
        `[RequestRecordPartsUseCase] Failed to map DTO to Record ID: ${
          error instanceof Error ? error.message : 'An unknown error occurred'
        }`,
        'INVALID_RECORD_ID',
        this.recordId,
        { recordId: dto.recordId, originalError: error }
      );
    }
  }

  private async gerRecordDataFromJudiciary() {
    // eslint-disable-next-line no-console
    console.log('[RequestRecordPartsUseCase] Getting Record Parts....');
    const parts =
      await this.externalRecordService.requestRecordData<RequestRecordDataArrayResponseDTO>(
        {
          module: 'Intervencion/ConsultarIntervencionExpediente',
          type: 'DATA',
          recordId: this.recordId,
          data: {
            ...(this.folder ? { carpeta: this.folder } : {}),
            numero: this.recordId.replace(/-/g, ''),
            contexto: { ...this.context },
            provieneSIAGPJ: false,
          },
        }
      );

    return parts.valorDevuelto || [];
  }

  private mapJudiciaryRecordToDomain(
    judiciaryRecord: Record<string, unknown>[]
  ): Part[] {
    const parts: Part[] = [];
    for (const rawPart of judiciaryRecord) {
      const domainOrError = PartMap.fromJudiciaryAPIToDomain(rawPart);
      if (domainOrError.isFailure) {
        // eslint-disable-next-line no-console
        console.error(
          '[RequestRecordPartsUseCase] Map Judiciary Record to Domain Error:',
          domainOrError.getErrorValue()
        );
        throw domainOrError.getErrorValue();
      }
      parts.push(domainOrError.getValue());
    }

    return parts;
  }
}
