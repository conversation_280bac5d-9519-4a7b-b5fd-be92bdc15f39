import { DomainError } from '../../../../shared/domain/errors/domain/domain-error';
import { ExternalServiceError } from '../../../../shared/domain/errors/domain/external-service-error';
import { ValidationError } from '../../../../shared/domain/errors/domain/validation-error';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';
import {
  ExternalRecordService,
  RequestRecordDataArrayResponseDTO,
} from '../../../../shared/services/judiciary/types';
import { RecordId } from '../../../record/domain/record-id';
import { Authorization } from '../../domain/Authorization';
import { AuthorizationMap } from '../../mappers/authorization-map';
import { RequestRecordAuthorizationsDTO } from './dto';

export class RequestRecordAuthorizationsUseCase {
  private recordId: string;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private context: any;

  private folder: string | undefined;

  constructor(private externalRecordService: ExternalRecordService) {}

  @LogUseCaseDTO public async execute(
    dto: RequestRecordAuthorizationsDTO
  ): Promise<Authorization[] | DomainError> {
    try {
      this.recordId = this.mapDTOtoRecordId(dto);
      this.context = dto.context;
      this.folder = dto.folder;
      const result = await this.gerRecordDataFromJudiciary();

      return this.mapJudiciaryRecordToDomain(result);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[RequestRecordAuthorizationsUseCase] Error executing use case:',
        JSON.stringify(error)
      );
      if (error instanceof DomainError) {
        // eslint-disable-next-line no-console
        console.log('[RequestRecordAuthorizationsUseCase] DomainError:', error);

        return error;
      }

      return new ExternalServiceError(
        `[RequestRecordAuthorizationsUseCase] Error executing use case: ${
          error instanceof Error ? error.message : 'An unknown error occurred'
        }`,
        dto.recordId,
        'RequestRecordAuthorizationsUseCase => execute',
        {
          module: 'Authorizations',
          originalError: JSON.stringify(error),
          requestData: dto,
        }
      );
    }
  }

  private mapDTOtoRecordId(dto: RequestRecordAuthorizationsDTO) {
    try {
      const recordIdOrError = RecordId.create(dto.recordId);
      if (recordIdOrError.isFailure) {
        throw recordIdOrError.getErrorValue();
      }

      return recordIdOrError.getValue().value;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[RequestRecordAuthorizationsUseCase] Error mapping DTO to Record ID:',
        JSON.stringify(error)
      );
      throw new ValidationError(
        `[RequestRecordAuthorizationsUseCase] Failed to map DTO to Record ID: ${
          error instanceof Error ? error.message : 'An unknown error occurred'
        }`,
        'INVALID_RECORD_ID',
        this.recordId,
        { recordId: dto.recordId, originalError: error }
      );
    }
  }

  private async gerRecordDataFromJudiciary() {
    // eslint-disable-next-line no-console
    console.log(
      '[RequestRecordAuthorizationsUseCase] Getting Record Authorizations....'
    );
    const authorizations =
      await this.externalRecordService.requestRecordData<RequestRecordDataArrayResponseDTO>(
        {
          module: 'DepositoJudicialController/ConsultarAutorizaciones',
          type: 'DATA',
          recordId: this.recordId,
          data: {
            ...(this.folder ? { carpeta: this.folder } : {}),
            numero: this.recordId.replace(/-/g, ''),
            contexto: { ...this.context },
          },
        }
      );

    return authorizations.valorDevuelto || [];
  }

  private mapJudiciaryRecordToDomain(
    judiciaryRecord: Record<string, unknown>[]
  ): Authorization[] {
    const authorizations: Authorization[] = [];
    for (const rawAuthorization of judiciaryRecord) {
      const domainOrError =
        AuthorizationMap.fromJudiciaryAPIToDomain(rawAuthorization);
      if (domainOrError.isFailure) {
        // eslint-disable-next-line no-console
        console.error(
          '[RequestRecordAuthorizationsUseCase] Map Judiciary Record to Domain Error:',
          domainOrError.getErrorValue()
        );
        throw domainOrError.getErrorValue();
      }
      authorizations.push(domainOrError.getValue());
    }

    return authorizations;
  }
}
