import { DomainError } from '../../../../shared/domain/errors/domain/domain-error';
import { ExternalServiceError } from '../../../../shared/domain/errors/domain/external-service-error';
import { ValidationError } from '../../../../shared/domain/errors/domain/validation-error';
import { LogUseCaseDTO } from '../../../../shared/infra/decorators/use-case';
import {
  ExternalRecordService,
  RequestRecordDataArrayResponseDTO,
} from '../../../../shared/services/judiciary/types';
import { RecordId } from '../../../record/domain/record-id';
import { Retention } from '../../domain/Retention';
import { RetentionMap } from '../../mappers/retention-map';
import { RequestRecordRetentionsDTO } from './dto';

export class RequestRecordRetentionsUseCase {
  private recordId: string;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private context: any;

  private folder: string | undefined;

  constructor(private externalRecordService: ExternalRecordService) {}

  @LogUseCaseDTO public async execute(
    dto: RequestRecordRetentionsDTO
  ): Promise<Retention[] | DomainError> {
    try {
      this.recordId = this.mapDTOtoRecordId(dto);
      this.context = dto.context;
      this.folder = dto.folder;
      const result = await this.gerRecordDataFromJudiciary();

      return this.mapJudiciaryRecordToDomain(result);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[RequestRecordRetentionsUseCase] Error executing use case:',
        JSON.stringify(error)
      );
      if (error instanceof DomainError) {
        // eslint-disable-next-line no-console
        console.log('[RequestRecordRetentionsUseCase] DomainError:', error);

        return error;
      }

      return new ExternalServiceError(
        `[RequestRecordRetentionsUseCase] Error executing use case: ${
          error instanceof Error ? error.message : 'An unknown error occurred'
        }`,
        dto.recordId,
        'RequestRecordRetentionsUseCase => execute',
        {
          module: 'Retentions',
          originalError: JSON.stringify(error),
          requestData: dto,
        }
      );
    }
  }

  private mapDTOtoRecordId(dto: RequestRecordRetentionsDTO) {
    try {
      const recordIdOrError = RecordId.create(dto.recordId);
      if (recordIdOrError.isFailure) {
        throw recordIdOrError.getErrorValue();
      }

      return recordIdOrError.getValue().value;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(
        '[RequestRecordRetentionsUseCase] Error mapping DTO to Record ID:',
        JSON.stringify(error)
      );
      throw new ValidationError(
        `[RequestRecordRetentionsUseCase] Failed to map DTO to Record ID: ${
          error instanceof Error ? error.message : 'An unknown error occurred'
        }`,
        'INVALID_RECORD_ID',
        this.recordId,
        { recordId: dto.recordId, originalError: error }
      );
    }
  }

  private async gerRecordDataFromJudiciary() {
    // eslint-disable-next-line no-console
    console.log(
      '[RequestRecordRetentionsUseCase] Getting Record Retentions....'
    );
    const retentions =
      await this.externalRecordService.requestRecordData<RequestRecordDataArrayResponseDTO>(
        {
          module: 'DepositoJudicialController/ConsultarRetenciones',
          type: 'DATA',
          recordId: this.recordId,
          data: {
            ...(this.folder ? { carpeta: this.folder } : {}),
            numero: this.recordId.replace(/-/g, ''),
            contexto: { ...this.context },
          },
        }
      );

    return retentions.valorDevuelto || [];
  }

  private mapJudiciaryRecordToDomain(
    judiciaryRecord: Record<string, unknown>[]
  ): Retention[] {
    const retentions: Retention[] = [];
    for (const rawRetention of judiciaryRecord) {
      const domainOrError = RetentionMap.fromJudiciaryAPIToDomain(rawRetention);
      if (domainOrError.isFailure) {
        // eslint-disable-next-line no-console
        console.error(
          '[RequestRecordRetentionsUseCase] Map Judiciary Record to Domain Error:',
          domainOrError.getErrorValue()
        );
        throw domainOrError.getErrorValue();
      }
      retentions.push(domainOrError.getValue());
    }

    return retentions;
  }
}
