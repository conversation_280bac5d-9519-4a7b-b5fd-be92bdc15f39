/* eslint-disable no-console */
import { Result } from '../../../shared/core/Result';
import { AuthenticationError } from '../../../shared/domain/errors/domain/authentication-error';
import { RecordExternalStorageService } from '../../../shared/services/zoho/crm/record/record-external-storage-service';
import { ScraperService } from '../../../shared/services/scrapper-service';
import { ExternalCredentialStorageService } from '../../../shared/services/zoho/crm/credentials/type';
import { AuthenticationData } from '../domain/AuthenticationData';
import { Credential } from '../domain/Credential';
import {
  GetTokenAndContextDTO,
  ServiceAuthDataDTO,
} from '../dto/authentication-data-dto';
import { AuthenticationDataMap } from '../mappers/authentication-data-map';
import { AuthenticationDataRepository } from '../repo/authentication-data-repository';

/**
 * Interface for external credentials retrieved from Zoho CRM
 */
interface IExternalCredentials {
  userName: string;
  password: string;
}
/**
 * Interface for authentication context used in error reporting
 */
interface IAuthenticationContext {
  startDate: Date;
  endDate?: Date;
  recordId?: string;
  internalRecordId?: string;
  username?: string;
  error?: unknown;
  stack?: string;
}
/**
 * Configuration constants for the authentication middleware
 */
const AUTHENTICATION_CONFIG = {
  MAX_RETRY_ATTEMPTS: 1, // Number of retries per credential before moving to next
  CACHE_VALIDITY_MINUTES: 15, // Cache validity period in minutes
} as const;
/**
 * AuthenticationDataMiddleware handles authentication for external judiciary services.
 *
 * This middleware:
 * 1. Retrieves credentials from Zoho CRM for a given recordId
 * 2. Checks for cached valid authentication data in DynamoDB
 * 3. Generates new tokens/context using ScraperService when needed
 * 4. Implements retry logic with multiple credentials
 * 5. Caches successful authentication data for performance optimization
 *
 * The middleware follows a retry strategy where each credential is attempted
 * up to MAX_RETRY_ATTEMPTS times before moving to the next credential.
 */
export class AuthenticationDataMiddleware {
  private externalCredentials: IExternalCredentials[] = [];

  private recordId: string | null = null;

  constructor(
    private readonly authenticationDataRepository: AuthenticationDataRepository,
    private readonly scraperService: ScraperService,
    private readonly externalCredentialStorageService: ExternalCredentialStorageService,
    private readonly recordExternalStorageService: RecordExternalStorageService
  ) {}

  /**
   * Initializes credentials for a specific recordId.
   * This method should be called once after creating the middleware instance.
   *
   * @param recordId - The record ID to initialize credentials for
   */
  public async initialize(recordId: string): Promise<void> {
    if (this.recordId === recordId && this.externalCredentials) {
      return; // Already initialized for this recordId
    }
    this.recordId = recordId;
    this.externalCredentials = await this.getCredentialsFromZohoCRM(recordId);
    console.log(
      `[AuthenticationDataMiddleware] Initialized with ${this.externalCredentials.length} credentials for recordId: ${recordId}`
    );
  }

  /**
   * Main entry point for getting service authentication data.
   *
   * This method implements the complete authentication flow:
   * 1. Retrieves credentials from Zoho CRM
   * 2. Checks for valid cached authentication data
   * 3. If no valid cache exists, attempts to generate new tokens
   * 4. Implements retry logic across multiple credentials
   *
   * @param recordId - The record ID to authenticate for
   * @param forceNewCredential - Force generation of new credentials (legacy parameter for compatibility)
   * @param shouldRefreshCurrentCredential - Force refresh of current credential (legacy parameter for compatibility)
   * @returns Result containing ServiceAuthDataDTO or error
   */
  public async getServiceAuthData(
    recordId: string,
    forceNewCredential = false,
    shouldRefreshCurrentCredential = false
  ): Promise<Result<ServiceAuthDataDTO>> {
    console.log(
      '[AuthenticationDataMiddleware] Getting service auth data for recordId:',
      recordId
    );
    try {
      // Initialize if not already done or if recordId changed
      if (this.recordId !== recordId || this.externalCredentials.length === 0) {
        await this.initialize(recordId);
      }
      // Step 1: Get credentials from Zoho CRM
      const externalCredentials = this.externalCredentials;
      // Step 2: Get cached authentication data from DynamoDB
      const cachedAuthData = await this.getCachedAuthenticationData(recordId);
      // Step 3: Check for valid cached credentials
      const validCachedCredential = this.findValidCachedCredential(
        cachedAuthData,
        externalCredentials
      );
      if (
        validCachedCredential &&
        !forceNewCredential &&
        !shouldRefreshCurrentCredential
      ) {
        console.log(
          '[AuthenticationDataMiddleware] Using valid cached credential for username:',
          validCachedCredential.username.value
        );

        return Result.ok(
          this.credentialToServiceAuthData(validCachedCredential)
        );
      }
      // Step 4: Generate new authentication data using retry logic
      const newAuthData = await this.generateAuthenticationDataWithRetry(
        recordId,
        externalCredentials,
        cachedAuthData
      );

      return Result.ok(newAuthData);
    } catch (error) {
      console.error(
        '[AuthenticationDataMiddleware] Failed to get service auth data:',
        error
      );

      return Result.fail<ServiceAuthDataDTO>(
        `Failed to generate authentication data: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  /**
   * Retrieves credentials from Zoho CRM for the given recordId.
   *
   * @param recordId - The record ID to get credentials for
   * @returns Array of external credentials with username and password
   */
  private async getCredentialsFromZohoCRM(
    recordId: string
  ): Promise<IExternalCredentials[]> {
    const authContext: IAuthenticationContext = {
      startDate: new Date(),
    };
    console.log(
      '[AuthenticationDataMiddleware] Getting credentials from Zoho CRM for recordId:',
      recordId
    );
    try {
      // Get the record from external storage
      const record = await this.getRecordFromExternalStorage(recordId);
      // Search for credentials associated with this record
      const partialCredentialsResult =
        await this.externalCredentialStorageService.searchCredentialsByInternalRecordId(
          record.id
        );
      if (partialCredentialsResult.isFailure) {
        throw partialCredentialsResult.getErrorValue();
      }
      const partialCredentials = partialCredentialsResult.getValue();
      if (partialCredentials.length === 0) {
        throw new AuthenticationError(
          'No user credentials found in external storage for this record',
          recordId,
          {
            ...authContext,
            recordId,
            internalRecordId: record.id,
            endDate: new Date(),
          }
        );
      }
      // Hydrate the credentials with full user data
      const fullCredentials = await this.hydrateCredentialsFromZoho(
        partialCredentials.map((credential) => credential.Usuario_del_PJ.id)
      );
      console.log(
        '[AuthenticationDataMiddleware] Found credentials in Zoho CRM:',
        fullCredentials.length
      );

      return fullCredentials;
    } catch (error) {
      console.error(
        '[AuthenticationDataMiddleware] Error getting credentials from Zoho CRM:',
        error
      );
      throw error;
    }
  }

  /**
   * Hydrates partial credential IDs with full credential data from Zoho.
   *
   * @param userCredentialIdList - List of credential IDs to hydrate
   * @returns Array of hydrated credentials with username and password
   */
  private async hydrateCredentialsFromZoho(
    userCredentialIdList: string[]
  ): Promise<IExternalCredentials[]> {
    const hydratedCredentials: IExternalCredentials[] = [];
    for (const userCredentialId of userCredentialIdList) {
      const credentialResult =
        await this.externalCredentialStorageService.searchByZohoId(
          userCredentialId
        );
      if (credentialResult.isFailure) {
        throw credentialResult.getErrorValue();
      }
      const credential = credentialResult.getValue();
      hydratedCredentials.push({
        userName: credential.data[0].Usuario,
        password: credential.data[0].Password,
      });
    }

    return hydratedCredentials;
  }

  /**
   * Gets cached authentication data from DynamoDB repository.
   * If no data exists, creates a new empty authentication data record.
   *
   * @param recordId - The record ID to get cached data for
   * @returns AuthenticationData from cache or newly created
   */
  private async getCachedAuthenticationData(
    recordId: string
  ): Promise<AuthenticationData> {
    console.log(
      '[AuthenticationDataMiddleware] Getting cached authentication data for recordId:',
      recordId
    );
    const cachedDataResult =
      await this.authenticationDataRepository.getByRecordId(recordId);
    if (cachedDataResult.isFailure) {
      console.log(
        '[AuthenticationDataMiddleware] No cached data found, creating new authentication data record'
      );
      const newAuthData = this.createEmptyAuthenticationData(recordId);
      await this.persistAuthenticationData(newAuthData);

      return newAuthData;
    }
    console.log(
      '[AuthenticationDataMiddleware] Found cached authentication data'
    );

    return cachedDataResult.getValue();
  }

  /**
   * Creates an empty authentication data record for a given recordId.
   *
   * @param recordId - The record ID to create authentication data for
   * @returns New empty AuthenticationData instance
   */
  private createEmptyAuthenticationData(recordId: string): AuthenticationData {
    return AuthenticationDataMap.toDomain({
      recordId,
      credentials: [],
    });
  }

  /**
   * Finds a valid cached credential that matches one of the external credentials.
   * A credential is valid if it exists in cache, matches an external username,
   * and is still within the validity period (15 minutes).
   *
   * @param cachedAuthData - Cached authentication data from DynamoDB
   * @param externalCredentials - External credentials from Zoho CRM
   * @returns Valid credential if found, null otherwise
   */
  private findValidCachedCredential(
    cachedAuthData: AuthenticationData,
    externalCredentials: IExternalCredentials[]
  ): Credential | null {
    const externalUsernames = externalCredentials.map((cred) => cred.userName);
    const matchingCachedCredentials =
      cachedAuthData.getCredentialsByUsername(externalUsernames);
    // Sort by last updated (most recent first)
    const sortedCredentials = matchingCachedCredentials.sort(
      (a, b) =>
        new Date(b.lastUpdated.value).getTime() -
        new Date(a.lastUpdated.value).getTime()
    );

    // Find the first valid credential
    return (
      sortedCredentials.find((credential) => credential.isCredentialValid()) ||
      null
    );
  }

  /**
   * Generates authentication data with retry logic across multiple credentials.
   *
   * This method implements the core retry strategy:
   * 1. Attempts each credential up to MAX_RETRY_ATTEMPTS times
   * 2. If a credential fails, moves to the next one
   * 3. Continues until all credentials are exhausted or success is achieved
   *
   * @param recordId - The record ID for error reporting
   * @param externalCredentials - Array of credentials to try
   * @param cachedAuthData - Cached authentication data to update
   * @returns ServiceAuthDataDTO with successful authentication
   */
  private async generateAuthenticationDataWithRetry(
    recordId: string,
    externalCredentials: IExternalCredentials[],
    cachedAuthData: AuthenticationData
  ): Promise<ServiceAuthDataDTO> {
    console.log(
      '[AuthenticationDataMiddleware] Starting authentication with retry logic...'
    );
    const authContext: IAuthenticationContext = {
      startDate: new Date(),
      recordId,
    };
    for (
      let credentialIndex = 0;
      credentialIndex < externalCredentials.length;
      credentialIndex++
    ) {
      const credential = externalCredentials[credentialIndex];
      console.log(
        `[AuthenticationDataMiddleware] Trying credential ${
          credentialIndex + 1
        }/${externalCredentials.length}: ${credential.userName} | ${
          credential.password
        }`
      );
      for (
        let attempt = 0;
        attempt <= AUTHENTICATION_CONFIG.MAX_RETRY_ATTEMPTS;
        attempt++
      ) {
        try {
          console.log(
            `[AuthenticationDataMiddleware] Attempt ${attempt + 1}/${
              AUTHENTICATION_CONFIG.MAX_RETRY_ATTEMPTS + 1
            } for username: ${credential.userName}`
          );
          const authData = await this.generateTokenAndContext({
            username: credential.userName,
            password: credential.password,
          });
          // Success! Update cache and return
          await this.updateCachedCredential(cachedAuthData, authData);
          console.log(
            `[AuthenticationDataMiddleware] Successfully authenticated with username: ${credential.userName}`
          );

          return authData;
        } catch (error) {
          console.log(
            `[AuthenticationDataMiddleware] Attempt ${
              attempt + 1
            } failed for username ${credential.userName}:`,
            error
          );
          // If this was the last attempt for this credential, move to next credential
          if (attempt === AUTHENTICATION_CONFIG.MAX_RETRY_ATTEMPTS) {
            console.log(
              `[AuthenticationDataMiddleware] All attempts exhausted for username: ${credential.userName}, moving to next credential`
            );
            break;
          }
          // Otherwise, retry with the same credential
          console.log(
            `[AuthenticationDataMiddleware] Retrying with same credential: ${credential.userName}`
          );
        }
      }
    }
    // All credentials exhausted
    throw new AuthenticationError(
      'All credentials have been exhausted. Unable to authenticate.',
      recordId,
      {
        ...authContext,
        endDate: new Date(),
        totalCredentialsTried: externalCredentials.length,
        maxRetryAttempts: AUTHENTICATION_CONFIG.MAX_RETRY_ATTEMPTS,
      }
    );
  }

  /**
   * Generates token and context using the ScraperService.
   *
   * @param credentials - Username and password to authenticate with
   * @returns ServiceAuthDataDTO with token and context
   */
  private async generateTokenAndContext(
    credentials: GetTokenAndContextDTO
  ): Promise<ServiceAuthDataDTO> {
    const tokenDataResult = await this.scraperService.getTokenAndContext(
      credentials
    );
    if (tokenDataResult.isFailure) {
      throw new Error(
        `ScraperService failed: ${tokenDataResult.getErrorValue()}`
      );
    }
    const { token, context } = tokenDataResult.getValue();

    return {
      username: credentials.username,
      token,
      context,
    };
  }

  /**
   * Updates the cached authentication data with new credential information.
   *
   * @param cachedAuthData - The cached authentication data to update
   * @param newAuthData - New authentication data to cache
   */
  private async updateCachedCredential(
    cachedAuthData: AuthenticationData,
    newAuthData: ServiceAuthDataDTO
  ): Promise<void> {
    cachedAuthData.updateCredentials([
      {
        username: newAuthData.username,
        token: newAuthData.token,
        context: newAuthData.context,
      },
    ]);
    await this.persistAuthenticationData(cachedAuthData);
  }

  /**
   * Converts a Credential domain object to ServiceAuthDataDTO.
   *
   * @param credential - The credential to convert
   * @returns ServiceAuthDataDTO
   */
  private credentialToServiceAuthData(
    credential: Credential
  ): ServiceAuthDataDTO {
    return {
      username: credential.username.value,
      context: credential.context.value,
      token: credential.token.value,
    };
  }

  /**
   * Persists authentication data to DynamoDB.
   *
   * @param authData - Authentication data to persist
   */
  private async persistAuthenticationData(
    authData: AuthenticationData
  ): Promise<void> {
    console.log(
      '[AuthenticationDataMiddleware] Persisting authentication data to DynamoDB'
    );
    await this.authenticationDataRepository.update(authData);
  }

  /**
   * Gets a record from external storage (Zoho CRM).
   *
   * @param recordId - The record ID to search for
   * @returns Record data from external storage
   */
  private async getRecordFromExternalStorage(recordId: string) {
    const recordResult =
      await this.recordExternalStorageService.searchForExternalId({
        module: 'Cobros_Judiciales',
        queryParam: 'Name',
        queryValue: recordId,
      });
    if (recordResult.isFailure) {
      throw recordResult.getErrorValue();
    }
    const record = recordResult.getValue();

    return record.data[0];
  }
}
