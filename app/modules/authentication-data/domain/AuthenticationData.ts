import { DateValueObject } from '../../../shared/common-value-objects/date-value-object';
import { SimpleEmptyTextValueObject } from '../../../shared/common-value-objects/simple-empty-text-value-object';
import { SimpleTextValueObject } from '../../../shared/common-value-objects/simple-text-value-object';
import { Result } from '../../../shared/core/Result';
import { RecordId } from '../../record/domain/record-id';
import { Credential } from './Credential';

export interface IAuthenticationDataProps {
  recordId: RecordId;
  credentials: Credential[];
  createdAt: DateValueObject;
  updatedAt: DateValueObject;
}
export class AuthenticationData {
  constructor(private props: IAuthenticationDataProps) {}

  get recordId(): RecordId {
    return this.props.recordId;
  }

  get credentials(): Credential[] {
    return this.props.credentials;
  }

  public updateCredentials(
    credentials: {
      username: string;
      token: string;
      context: string;
    }[]
  ) {
    const updatedExistingCredentials = this.props.credentials.map(
      (existingCredential) => {
        const presentCredential = credentials.find(
          (credential) =>
            existingCredential.username.value === credential.username
        );
        if (presentCredential) {
          existingCredential.updateCredential({
            token: presentCredential.token,
            context: presentCredential.context,
          });
        }

        return existingCredential;
      }
    );
    const existingUsernames = updatedExistingCredentials.map(
      (cred) => cred.username.value
    );
    const newCredentials = credentials
      .filter((cred) => !existingUsernames.includes(cred.username))
      .map((newCred) => {
        const credentialOrError = Credential.create({
          username: SimpleTextValueObject.create(
            'username',
            newCred.username
          ).getValue(),
          token: SimpleEmptyTextValueObject.create(
            'token',
            newCred.token
          ).getValue(),
          context: SimpleEmptyTextValueObject.create(
            'context',
            newCred.context
          ).getValue(),
          lastUpdated: DateValueObject.create('lastUpdated').getValue(),
        });

        return credentialOrError.getValue();
      });
    const updatedAt = DateValueObject.create('updatedAt');
    this.props.credentials = [...updatedExistingCredentials, ...newCredentials];
    this.props.updatedAt = updatedAt.getValue();
  }

  get createdAt(): DateValueObject {
    return this.props.createdAt;
  }

  get updatedAt(): DateValueObject {
    return this.props.updatedAt;
  }

  public getCredentialsByUsername(usernameList: string[]): Credential[] {
    return this.props.credentials.filter((credential) =>
      usernameList.includes(credential.username.value)
    );
  }

  public static create(props: IAuthenticationDataProps) {
    const data = new AuthenticationData({
      ...props,
    });

    return Result.ok<AuthenticationData>(data);
  }
}
