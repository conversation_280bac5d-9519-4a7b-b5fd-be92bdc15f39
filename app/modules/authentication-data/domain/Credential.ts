import { DateValueObject } from '../../../shared/common-value-objects/date-value-object';
import { SimpleEmptyTextValueObject } from '../../../shared/common-value-objects/simple-empty-text-value-object';
import { SimpleTextValueObject } from '../../../shared/common-value-objects/simple-text-value-object';
import { Result } from '../../../shared/core/Result';

export interface ICredentialProps {
  username: SimpleTextValueObject;
  token: SimpleEmptyTextValueObject;
  context: SimpleEmptyTextValueObject;
  lastUpdated: DateValueObject;
}
export class Credential {
  constructor(private props: ICredentialProps) {}

  get username(): SimpleTextValueObject {
    return this.props.username;
  }

  get token(): SimpleEmptyTextValueObject {
    return this.props.token;
  }

  get context(): SimpleEmptyTextValueObject {
    return this.props.context;
  }

  get lastUpdated(): DateValueObject {
    return this.props.lastUpdated;
  }

  public updateCredential(value: { token: string; context: string }) {
    const tokenOrError = SimpleEmptyTextValueObject.create(
      'token',
      value.token
    );
    const contextOrError = SimpleEmptyTextValueObject.create(
      'context',
      value.context
    );
    const lastUpdatedOrError = DateValueObject.create('lastUpdated');
    const dbCombined = Result.combine([
      tokenOrError,
      contextOrError,
      lastUpdatedOrError,
    ]);
    if (dbCombined.isFailure) {
      throw dbCombined.getErrorValue();
    }
    this.props.token = tokenOrError.getValue();
    this.props.context = contextOrError.getValue();
    this.props.lastUpdated = lastUpdatedOrError.getValue();
  }

  /**
   * Validates if the credential is still valid based on the last update time.
   * A credential is considered valid if it has both token and context,
   * and was updated less than 15 minutes ago.
   * @returns true if the credential is valid, false otherwise
   */
  public isCredentialValid(): boolean {
    try {
      if (!this.props.token || !this.props.context) {
        return false;
      }
      const lastUpdated = new Date(this.props.lastUpdated.value);
      const now = new Date();
      const timeDifference = now.getTime() - lastUpdated.getTime();
      const minutesPassed = timeDifference / (1000 * 60);

      return minutesPassed < 15;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('isCredentialValid error:', error);

      return false;
    }
  }

  public static create(props: ICredentialProps) {
    const data = new Credential({
      ...props,
    });

    return Result.ok<Credential>(data);
  }
}
