import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import { ServiceConfig } from '../lib/shared/service-name-util';
import { DynamoDBStack } from '../lib/stacks/dynamodb-stack';
import { env } from 'process';
import { SecretManagerStack } from '../lib/stacks/secret-manager-stack';
import { RecordStack } from '../lib/stacks/record/index-stack';
import { CronJobStack } from '../lib/stacks/cron-job-stack';
import { QueueStack } from '../lib/stacks/sqs-queue-stack';

const app = new cdk.App();
if (env.AWS_ACCOUNT_ID && env.AWS_REGION) {
  const service = new ServiceConfig();
  const dynamodbStack = new DynamoDBStack(
    app,
    service.StackNameGenerator('dynamodb')
  );
  const secretManagerStack = new SecretManagerStack(
    app,
    service.StackNameGenerator('secret-manager')
  );
  const queueStack = new QueueStack(
    app,
    service.StackNameGenerator('sqs-queue')
  );
  new RecordStack(app, service.StackNameGenerator('record'), {
    dynamodbStack,
    secretManagerStack,
    queueStack,
  });
  new CronJobStack(app, service.StackNameGenerator('cron-job'), {
    queueStack,
    secretManagerStack,
  });
} else {
  // eslint-disable-next-line no-console
  console.log('Missing env variables');
}
